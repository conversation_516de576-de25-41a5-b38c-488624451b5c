@props([
    'table_name' => '',
    'callback' => '',
    'current_data_source_type' => 'hardcoded',
    'current_data_source_id' => null,
    'available_data_sources' => [],
    'processed_columns' => [],
    'hidden_columns' => [],
    'available_field_list' => []
])

    <!-- Header -->
    <div class="p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
            <div class="flex gap-2">

                <button type="button"
                        @click="showAddColumn = !showAddColumn"
                        class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                    + Column
                </button>
                <button type="button"
                        hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/show_all_columns"
                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                        hx-target=".data_table"
                        hx-swap="outerHTML"
                        class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                    Show All
                </button>
                <button type="button"
                        hx-post="{{ APP_ROOT }}/api/data_table_storage/hide_all_columns"
                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                        hx-target=".data_table"
                        hx-swap="outerHTML"
                        class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                    Hide All
                </button>
            </div>
        </div>

        <!-- Data Source Selector -->
        <div class="flex items-center space-x-3 mb-3">
            <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
            <select id="data-source-select"
                    hx-post="{{ APP_ROOT }}/api/data_table_storage/update_data_source_and_columns"
                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML"
                    hx-trigger="change"
                    name="data_source_selection"
                    class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                <option value="hardcoded" {{ $current_data_source_type === 'hardcoded' ? 'selected' : '' }}>
                    Default (Hardcoded Data)
                </option>
                @if(!empty($available_data_sources))
                    @php
                        $grouped_sources = [];
                        foreach ($available_data_sources as $source) {
                            $category = $source['category'];
                            if (!isset($grouped_sources[$category])) {
                                $grouped_sources[$category] = [];
                            }
                            $grouped_sources[$category][] = $source;
                        }

                        $category_labels = [
                            'data_table' => 'Data Tables',
                            'email' => 'Email & Campaigns',
                            'users' => 'User Management',
                            'system' => 'System Tables',
                            'autodesk' => 'Autodesk Integration',
                            'other' => 'Other'
                        ];
                    @endphp
                    @foreach($grouped_sources as $category => $sources)
                        <optgroup label="{{ $category_labels[$category] ?? ucfirst($category) }}">
                            @foreach($sources as $source)
                                <option value="{{ $source['id'] }}"
                                        {{ $current_data_source_id == $source['id'] ? 'selected' : '' }}>
                                    {{ $source['name'] }}
                                    @if($source['description'])
                                        - {{ $source['description'] }}
                                    @endif
                                </option>
                            @endforeach
                        </optgroup>
                    @endforeach
                @endif
            </select>
            @if($current_data_source_type === 'data_source' && $current_data_source_id)
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Data Source
                    </span>
            @else
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Hardcoded
                    </span>
            @endif
        </div>

        <!-- Default Configuration Option -->
        <div class="flex items-center space-x-3 mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
            <div class="flex items-center">
                <input type="checkbox"
                       id="apply_default_config_{{ $table_name }}"
                       hx-post="{{ APP_ROOT }}/api/unified_field_definitions/apply_default_config"
                       hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                       hx-target=".data_table"
                       hx-swap="outerHTML"
                       hx-trigger="change"
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                <label for="apply_default_config_{{ $table_name }}" class="ml-2 text-xs font-medium text-gray-700">
                    Apply default field configuration
                </label>
            </div>
            <div class="flex-1">
                <p class="text-xs text-gray-600">
                    Uses the "Show by default" settings from unified field definitions to configure initial column display.
                </p>
            </div>
        </div>

        <!-- Add Column Form -->
        <div x-show="showAddColumn" x-transition class="mb-3 p-2 bg-blue-50 rounded border">
            <form hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_column"
                  hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                  hx-target=".data_table"
                  hx-swap="outerHTML"
                  @htmx:after-request="showAddColumn = false; newColumnName = ''"
                  class="flex gap-2 items-center">
                <input type="text"
                       name="column_name"
                       x-model="newColumnName"
                       placeholder="Column name (e.g., Contact Info)"
                       class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                       required>
                <button type="submit"
                        class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Add
                </button>
                <button type="button"
                        @click="showAddColumn = false"
                        class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                    Cancel
                </button>
            </form>
        </div>

        <div class="text-xs text-gray-500">
            Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
        </div>
    </div>

    <!-- Column List -->
    <div class="flex-1 overflow-y-auto p-4">
        <form class="sortable column-sortable space-y-3"
              hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/reorder_columns"
              hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "data_source": "{{ $current_data_source_id }}"}'
              hx-target=".data_table"
              hx-swap="outerHTML"
              hx-trigger="end"
              data-table-name="{{ $table_name }}"
              data-callback="{{ $callback }}"
              data-data-source="{{ $current_data_source_id }}"
              x-ref="columnList">
            @foreach($processed_columns as $column)
                <div class="border border-gray-200 rounded-lg bg-white"
                     data-column-id="{{ $column['id'] }}">
                    <input type="hidden" name="column_order[]" value="{{ $column['id'] }}"/>

                    <!-- Column Header -->
                    <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">
                        <!-- Column Drag Handle -->
                        <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 8h16M4 16h16"/>
                            </svg>
                        </div>

                        <!-- Visibility Checkbox -->
                        <label class="flex items-center flex-1 cursor-pointer">
                            @php
                                $is_checked = !in_array($column['id'], $hidden_columns);
                            @endphp
                            <input type="checkbox"
                                   {{ $is_checked ? 'checked' : '' }}
                                   hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/toggle_column"
                                   hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "data_source": "{{ $current_data_source_id }}"}'
                                   hx-target=".data_table"
                                   hx-swap="outerHTML"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                            <span class="text-sm font-medium text-gray-900">{{ $column['label'] }}</span>
                        </label>

                        <!-- Column Type Indicator -->
                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $column['filter'] ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $column['filter'] ? 'Filterable' : 'Display' }}
                                </span>
                        </div>

                        <!-- Field Count Badge -->
                        <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    {{ count($column['fields']) }} field{{ count($column['fields']) !== 1 ? 's' : '' }}
                                </span>
                        </div>

                        <!-- Rename Column Button -->
                        <button type="button"
                                @click="toggleRenameInput({{ $loop->index }})"
                                class="ml-2 text-blue-400 hover:text-blue-600"
                                title="Rename Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                        </button>

                        <!-- Remove Column Button -->
                        <button type="button"
                                hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_column"
                                hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                hx-target=".data_table"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to remove this column?"
                                class="ml-2 text-red-400 hover:text-red-600"
                                title="Remove Column">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Rename Input (Hidden by default) -->
                    <div x-ref="renameInput_{{ $loop->index }}" style="display: none;"
                         class="p-2 bg-blue-50 border-t border-blue-200">
                        <form hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/rename_column"
                              hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                              hx-target=".data_table"
                              hx-swap="outerHTML"
                              @htmx:after-request="hideRenameInput({{ $loop->index }})"
                              class="flex gap-2 items-center">
                            <input type="text"
                                   name="new_label"
                                   value="{{ $column['label'] }}"
                                   placeholder="Enter new column name"
                                   class="flex-1 text-sm border border-blue-300 rounded px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                   @keydown.escape="hideRenameInput({{ $loop->index }})"
                                   @keydown.enter="$event.preventDefault(); $event.target.closest('form').requestSubmit()">
                            <button type="submit"
                                    class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                                Save
                            </button>
                            <button type="button"
                                    @click="hideRenameInput({{ $loop->index }})"
                                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                Cancel
                            </button>
                        </form>
                    </div>

                    <!-- Fields Container -->
                    <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">
                        <!-- Add Field Dropdown -->
                        <div class="mb-2">
                            <select hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_field_to_column"
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-trigger="change[target.value != '']"
                                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                    name="field_name"
                                    @htmx:after-request="$event.target.value = ''"
                                    class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                <option value="">+ Add field...</option>
                                @php
                                    // Use the cached field list from above to avoid repeated database calls
                                    // $available_field_list is already set in the PHP block at the top
                                @endphp
                                @foreach($available_field_list as $field)
                                    @if(!in_array($field, $column['fields']))
                                        <option value="{{ $field }}">{{ $field }}</option>
                                    @endif
                                @endforeach
                            </select>
                        </div>

                        <!-- Add Action Button -->
                        <div class="mb-2">
                            <button type="button"
                                    @click="toggleActionForm({{ $loop->index }})"
                                    class="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 border border-green-300">
                                + Add Action Button
                            </button>
                        </div>

                        <!-- Add Action Button Form (Hidden by default) -->
                        <div x-ref="actionForm_{{ $loop->index }}" style="display: none;"
                             class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
                            <form hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_action_button"
                                  hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                  hx-target=".data_table"
                                  hx-swap="outerHTML"
                                  @htmx:after-request="hideActionForm({{ $loop->index }})"
                                  class="space-y-2">

                                <!-- Template Dropdown -->
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
                                    <select name="template" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select template...</option>
                                        @php
                                            // Cache template scanning to avoid repeated filesystem operations
                                            static $cached_action_templates = null;
                                            if ($cached_action_templates === null) {
                                                $templates = [];
                                                $template_dirs = [
                                                    FS_SYS_COMPONENTS . DS . 'edges',
                                                    FS_RESOURCES . DS . 'components' . DS . 'edges'
                                                ];

                                                foreach ($template_dirs as $template_dir) {
                                                    if (is_dir($template_dir)) {
                                                        $files = scandir($template_dir);
                                                        foreach ($files as $file) {
                                                            if (pathinfo($file, PATHINFO_EXTENSION) === 'php' && strpos($file, '.edge.') !== false) {
                                                                $template_name = str_replace('.edge.php', '', $file);
                                                                if (strpos($template_name, 'action-') === 0) { // Only show action templates
                                                                    $templates[] = $template_name;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }

                                                // Remove duplicates and sort
                                                $templates = array_unique($templates);
                                                sort($templates);
                                                $cached_action_templates = $templates;
                                            } else {
                                                $templates = $cached_action_templates;
                                            }
                                        @endphp
                                        @foreach($templates as $template)
                                            <option value="{{ $template }}">{{ ucwords(str_replace(['-', '_'], ' ', $template)) }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Field Dropdown -->
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Field to
                                        pass:</label>
                                    <select name="field" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select field...</option>
                                        @foreach($available_field_list as $field)
                                            <option value="{{ $field }}">{{ $field }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- Icon Dropdown -->
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
                                    <select name="icon" required
                                            class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                                        <option value="">Select icon...</option>
                                        <option value="edit">✏️ Edit</option>
                                        <option value="delete">🗑️ Delete</option>
                                        <option value="view">👁️ View</option>
                                        <option value="download">⬇️ Download</option>
                                        <option value="upload">⬆️ Upload</option>
                                        <option value="copy">📋 Copy</option>
                                        <option value="share">🔗 Share</option>
                                        <option value="settings">⚙️ Settings</option>
                                        <option value="info">ℹ️ Info</option>
                                        <option value="warning">⚠️ Warning</option>
                                        <option value="success">✅ Success</option>
                                        <option value="error">❌ Error</option>
                                    </select>
                                </div>

                                <!-- Form Buttons -->
                                <div class="flex gap-2">
                                    <button type="submit"
                                            class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                                        Add Action
                                    </button>
                                    <button type="button"
                                            @click="hideActionForm({{ $loop->index }})"
                                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Existing Fields -->
                        @foreach($column['fields'] as $field)
                            <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                 data-field-name="{{ $field }}">
                                <!-- Field Drag Handle -->
                                <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 8h16M4 16h16"/>
                                    </svg>
                                </div>
                                <span class="text-gray-700">{{ $field }}</span>
                                <!-- Remove Field Button -->
                                <button type="button"
                                        hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_field_from_column"
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "field_name": "{{ $field }}"}'
                                        class="text-gray-400 hover:text-red-600 ml-1">
                                    <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        @endforeach

                        <!-- Existing Action Buttons -->
                        @if(isset($column['action_buttons']) && !empty($column['action_buttons']))
                            @foreach($column['action_buttons'] as $action)
                                <div class="action-item inline-flex items-center gap-x-1 bg-green-50 px-2 py-1 m-1 rounded border border-green-200 text-xs cursor-move"
                                     data-action-id="{{ $action['id'] }}">
                                    <!-- Action Drag Handle -->
                                    <div class="action-drag-handle text-green-400 hover:text-green-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8h16M4 16h16"/>
                                        </svg>
                                    </div>

                                    <!-- Icon Display -->
                                    <span class="text-sm">
                                            @switch($action['icon'])
                                            @case('edit') ✏️ @break
                                            @case('delete') 🗑️ @break
                                            @case('view') 👁️ @break
                                            @case('download') ⬇️ @break
                                            @case('upload') ⬆️ @break
                                            @case('copy') 📋 @break
                                            @case('share') 🔗 @break
                                            @case('settings') ⚙️ @break
                                            @case('info') ℹ️ @break
                                            @case('warning') ⚠️ @break
                                            @case('success') ✅ @break
                                            @case('error') ❌ @break
                                            @default 🔘 @break
                                        @endswitch
                                        </span>

                                    <span class="text-gray-700">{{ ucwords(str_replace(['-', '_'], ' ', $action['template'])) }}</span>
                                    <span class="text-gray-500">({{ $action['field'] }})</span>

                                    <!-- Remove Action Button -->
                                    <button type="button"
                                            hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_action_button"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "action_id": "{{ $action['id'] }}"}'
                                            hx-confirm="Are you sure you want to remove this action button?"
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @endif

                        <!-- Drop Zone Indicator -->
                        @if(empty($column['fields']) && empty($column['action_buttons']))
                            <div class="text-xs text-gray-400 text-center py-2">
                                Drop fields or action buttons here, or use dropdowns above
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </form>
    </div>

    <!-- Footer Actions -->
    <div class="p-4 border-t border-gray-200 bg-gray-50">
        <div class="flex justify-between items-center">
            <div class="flex gap-2 items-center">
                <button type="button"
                        hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/hide_all_columns"
                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                        hx-target=".data_table"
                        hx-swap="outerHTML"
                        class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                    Hide All
                </button>
                @php
                    $total_fields = array_sum(array_map(function($col) { return count($col['fields']); }, $processed_columns));
                @endphp
                <span class="text-xs text-gray-500">{{ count($processed_columns) }} columns, {{ $total_fields }} fields</span>
            </div>
            <button type="button"
                    @click="open = false"
                    class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                Done
            </button>
        </div>
    </div>
