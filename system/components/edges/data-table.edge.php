@props([
    'title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'items' => [], // data array of items
    'id_count' => edge::id_count(),
    'columns' => [], // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
    'available_fields' => [], // Array of available fields for column manager
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],
    'just_rows' => false, // just return rows
    'just_body' => false, // just return body
    'just_table' => false, // just return table no extras
    'items_per_page' => 30, //max items to display before pagination
    'current_page_num' => 1,
    'class' => '', //extra classes
    'sort_column' => '',
    'sort_direction' => '',
    'auto_update' => false,
    'callback' => null,
    'table_name' => str_replace('/','_',strtolower(APP_PATH . '_' . CURRENT_PAGE)),
    'db_table' => '',
    'show_column_manager' => true,
    'column_preferences' => [],
    'criteria' => [],
    'config' => null,
    'data_source_type' => 'hardcoded',
    'data_source_id' =>  null
])
@use system\data_table_storage


@print_rr([
'items' => array_slice($items,rand(0,count($items)-(count($items)>5 ? 5 : count($items))??,5),
'count' => count($items),
'columns' => $columns,
'column_count' => count($columns),
'available_fields' => $available_fields
],'items')

@php
    // Ensure columns is always an array to prevent count() errors
    $columns = $columns ?? [];
    $items = $items ?? [];
    $available_fields = $available_fields ?? [];

    // Auto-load data from data source if no items provided and no callback
    if (empty($items) && empty($callback)) {
        $user_id = $_SESSION['user_id'] ?? null;

        // Try to get data from data source
        $data_result = data_table_storage::get_table_data($table_name, [], $criteria, $user_id, $data_source_id);
        $columns = count($columns) > 0 ? $columns : ($data_result['config']['columns'] ?? []);
        // Debug removed for production
        if (is_array($columns)) {
            foreach ($columns as $col) {
                $available_fields[] = $col['field'];
            }
        }
        // Debug auto-loading
        tcs_log("Auto-loading data: table=$table_name, success=" . ($data_result['success'] ? 'true' : 'false') . ", source=" . ($data_result['source'] ?? 'unknown') . ", count=" . ($data_result['count'] ?? 0), 'data_table_saga');
        // Debug removed for production
        // Load data if successful, regardless of source type (could be data_source, hardcoded, or hardcoded_fallback)
        if ($data_result['success']) {
            $items = $data_result['data'];

            // Auto-generate columns from data source if no columns provided or default columns
            // Only do this if we have data and are using default columns
            if (!empty($items) && empty($columns)) {
                $columns = [];
                $available_fields = [];
                // Get first item to determine columns
                $first_item = reset($items);
                if ($first_item) {
                    foreach (array_keys($first_item) as $field) {
                        $columns[] = [
                            'label' => ucwords(str_replace('_', ' ', $field)),
                            'field' => $field,
                            'filter' => false,
                            'extra_parameters' => ''
                        ];
                        $available_fields[] = $field;
                    }
                }
            }
        }

        // Get column preferences for this table
        $config = $data_result['config'] ?? null;
        $column_preferences = $config;

    }
    if ($table_name == '') {
        $table_name = str_replace('/','_',strtolower(APP_PATH . '_' . CURRENT_PAGE));
    }
    print_rr([
        'data_table_template_start' => [
            'table_name' => $table_name,
            'has_column_preferences' => !empty($column_preferences),
            'hidden_count' => count($column_preferences['hidden'] ?? []),
            'columns_count' => count($columns ?? []),
            'show_column_manager' => $show_column_manager ?? 'not set'
        ]
    ], 'data_table_template_start');
    // Get configuration for determining current data source (outside the auto-loading block)
    $user_id = $_SESSION['user_id'] ?? null;
    if (!$config) {
        $config = data_table_storage::get_configuration($table_name, $user_id);
    }

    // Ensure column_preferences is properly set
    // Priority: 1) Passed in column_preferences, 2) Config from database, 3) Fresh from API
    if (!isset($column_preferences) || empty($column_preferences)) {
        if ($config && isset($config['configuration'])) {
            $column_preferences = $config['configuration'];
        } else {
            // If no column preferences passed in and no config, get fresh from database
          // $column_preferences = get_column_preferences($table_name);
        }
    }

    // If we have a structure, rebuild the columns array from it to ensure proper order and field assignments
    if (!empty($column_preferences['structure'])) {
        $structure_columns = [];
        foreach ($column_preferences['structure'] as $struct_col) {
            // Find the original column definition to preserve other properties
            $original_col = null;
            if (is_array($columns)) {
                foreach ($columns as $col) {
                    if ($col['label'] === $struct_col['label'] || (isset($col['field']) && in_array($col['field'], $struct_col['fields'] ?? []))) {
                        $original_col = $col;
                        break;
                    }
                }
            }

            // Build the column using structure data with original properties as fallback
            $structure_columns[] = [
                'label' => $struct_col['label'],
                'field' => $struct_col['fields'][0] ?? ($original_col['field'] ?? ''),
                'fields' => $struct_col['fields'] ?? [$original_col['field'] ?? ''],
                'filter' => $struct_col['filter'] ?? ($original_col['filter'] ?? true),
                'visible' => $struct_col['visible'] ?? true,
                'structure_id' => $struct_col['id'],
                'action_buttons' => $struct_col['action_buttons'] ?? [],
                // Preserve other original properties
                'replacements' => $original_col['replacements'] ?? null,
                'content' => $original_col['content'] ?? null,
                'class' => $original_col['class'] ?? '',
                'extra_parameters' => $original_col['extra_parameters'] ?? ''
            ];
        }
        $columns = $structure_columns;
    }

    // Determine current data source information for column manager

    if ($config) {
        // Check database field first (most reliable)
        if (!empty($config['data_source_id'])) {
            $data_source_type = 'data_source';
            $data_source_id = $config['data_source_id'];
        } elseif (!empty($column_preferences)) {
            // Fallback to configuration JSON
            $data_source_type = $column_preferences['data_source_type'] ?? 'hardcoded';
            $data_source_id = $column_preferences['data_source_id'] ?? null;
        }
    }
    print_rr([
        'data_table_template_mid' => [
            'table_name' => $table_name,
            'has_column_preferences' => !empty($column_preferences),
            'hidden_count' => count($column_preferences['hidden'] ?? []),
            'columns_count' => count($columns ?? []),
            'show_column_manager' => $show_column_manager ?? 'not set',
            'data_source_type' => $data_source_type,
            'data_source_id' => $data_source_id
        ]
    ], 'data_table_template_mid');
    // Check if we should override hardcoded data with data source data
    if ($data_source_type === 'data_source' && $data_source_id ) {
        // Configuration says to use data source, but we have hardcoded items - override them
        $user_id = $_SESSION['user_id'] ?? null;
        $data_result = data_table_storage::get_table_data($table_name, $items, $criteria, $user_id, $data_source_id);

        tcs_log("Overriding hardcoded data with data source: table=$table_name, success=" . ($data_result['success'] ? 'true' : 'false') . ", source=" . ($data_result['source'] ?? 'unknown') . ", count=" . ($data_result['count'] ?? 0), 'data_table_saga');

        if ($data_result['success'] && $data_result['source'] !== 'hardcoded_fallback') {
            $items = $data_result['data'];

            // Update available fields if we got new data
            if (!empty($data_result['columns'])) {
                $available_fields = [];
                foreach ($data_result['columns'] as $col) {
                    $available_fields[] = $col['field'] ?? $col;
                }
            }
        }
    }

    // Debug output
    tcs_log("Data table template: table=$table_name, type=$data_source_type, id=$data_source_id", 'data_table_saga');
    print_rr([
        'data_table_template_end' => [
            'table_name' => $table_name,
            'has_column_preferences' => !empty($column_preferences),
            'hidden_count' => count($column_preferences['hidden'] ?? []),
            'columns_count' => count($columns ?? []),
            'show_column_manager' => $show_column_manager ?? 'not set'
        ]
    ], 'data_table_template_end');
    $table_name = $table_name == '' ? str_replace('/','_',strtolower(APP_PATH . '_' . CURRENT_PAGE)) : $table_name;
@endphp

@if (!$just_body && !$just_rows && !$just_table)
    @if($auto_update)
        <x-forms-input
                type='hidden'
                name='last_update'
                :value='date("Y-m-d H:i:s")'
                hx-post='{{ APP_ROOT }}/api/system/update'
                hx-swap='outerHTML'
                hx-trigger='every 10s'
        />
    @endif
    <input type="hidden" class="data_table_filter" name="callback" value="{{ $callback }}">
    <input type="hidden" class="data_table_filter" name="table_name" value="{{ $table_name }}">
    <input type="hidden" class="data_table_filter" name="data_source_id" value="{{ $data_source_id }}">
    <div class="relative">
        @if($show_column_manager)
            <div class="absolute top-0 right-0 z-10 p-2">
                <x-data-table-column-manager
                        :columns="$columns"
                        :table_name="$table_name"
                        :callback="$callback"
                        :column_preferences="$column_preferences"
                        :db_table="$db_table"
                        :available_fields="$available_fields"
                        :current_data_source_type="$data_source_type"
                        :current_data_source_id="$data_source_id"
                ></x-data-table-column-manager>
            </div>
        @endif
    </div>
@endif
@if (!$just_body && !$just_rows)
    @if (empty($columns) && empty($items))
        <!-- No columns and no items configured fallback -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center data_table" >
            <div class="flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-red-800 mb-2">Data Table Not Configured</h3>
            <p class="text-red-700 mb-4">
                This data table has no columns or data configured. Please set up both columns and a data source to display information.
            </p>
            @if($show_column_manager)
                <p class="text-sm text-red-600 mb-2">
                    Use the column manager (⚙️) in the top-right corner to configure columns and data sources for this table.
                </p>
            @endif
            <div class="text-xs text-red-500 bg-red-100 rounded p-2 mt-4">
                <strong>Table:</strong> {{ $table_name }}<br>
                <strong>Data Source Type:</strong> {{ $data_source_type ?? 'not set' }}<br>
                <strong>Data Source ID:</strong> {{ $data_source_id ?? 'not set' }}
            </div>
        </div>
    @elseif (empty($columns))
        <!-- No columns configured fallback -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center data_table">
            <div class="flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-yellow-800 mb-2">No Columns Configured</h3>
            <p class="text-yellow-700 mb-4">
                This data table has no columns configured. Please configure columns to display data.
            </p>
            @if($show_column_manager)
                <p class="text-sm text-yellow-600">
                    Use the column manager (⚙️) in the top-right corner to configure columns for this table.
                </p>
            @endif
        </div>
    @else
        <table class="min-w-full border-collapse search_target data_table {{ $class }}">
            <thead>
            <tr>

                @foreach($columns as $col)
                @php
                    // Use structure data if available (columns array is now built from structure)
                    if (isset($col['structure_id'])) {
                        $column_id = $col['structure_id'];
                        $is_hidden = !$col['visible'];
                        // Ensure $col_fields is a flat array of field names
                        $col_fields = is_array($col['fields']) ? $col['fields'] : [$col['fields']];
                        // Flatten nested arrays if they exist
                        $col_fields = array_merge(...array_map(fn($f) => is_array($f) ? $f : [$f], $col_fields));
                        $col_field = $col['field'];
                        $col_label = $col['label'];
                    } else {
                        // Fallback for non-structure columns
                        $column_id = 'col_' . $loop->index . '_' . md5($col['label']);
                        $is_hidden = isset($column_preferences['hidden']) && in_array($column_id, $column_preferences['hidden']);
                        $col_fields = [$col['field']];
                        $col_field = $col['field'];
                        $col_label = $col['label'];
                    }
                @endphp
                @if(!$is_hidden)
                    <th scope="col"
                        class="{{ $loop->first ? 'relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8' : 'relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell' }}"
                        style="isolation: isolate;"
                        data-column-field="{{ $col_field }}">
                        <x-data-table-filter
                                :label="$col_label"
                                :col="$col"
                                :sort_col="$sort_column"
                                :sort_dir="$sort_direction"
                                :callback="$callback"
                                :id_count="$id_count"
                        ></x-data-table-filter>
                    </th>
                @endif
            @endforeach
        </tr>
        </thead>
@endif <!-- just_body & rows -->
        @if (!$just_rows)
            <tbody class="bg-white data_table_body">
            @endif <!-- just rows -->
            @if (empty($items))
                <!-- No items/rows configured fallback -->
                <tr>
                    <td colspan="{{ count($columns) > 0 ? count($columns) : 1 }}" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
                            <p class="text-gray-500 mb-4">
                                There are no rows configured or available for this table.
                            </p>
                            @if($data_source_type === 'hardcoded')
                                <p class="text-sm text-gray-400">
                                    This table is configured to use hardcoded data, but no data has been provided.
                                </p>
                            @elseif($data_source_type === 'data_source' && $data_source_id)
                                <p class="text-sm text-gray-400">
                                    This table is configured to use data source ID: {{ $data_source_id }}, but no data was returned.
                                </p>
                            @else
                                <p class="text-sm text-gray-400">
                                    No data source has been configured for this table.
                                </p>
                            @endif
                        </div>
                    </td>
                </tr>
            @else
                @foreach ($items as $item)
                @if ($loop->first)
                    {{-- Debug removed for production --}}
                @endif
                <tr class="border-t {{ $loop->first ? 'border-gray-300' : 'border-gray-200' }} {{ $rows['class_postfix'] }}"
                    id="{{ $rows['id_prefix'] . $item[$rows['id_field']] . $rows['id_postfix'] }}" {{ $rows['extra_parameters'] }}>
                    @foreach ($columns as $col)
                        @php
                            // Use structure data if available (columns array is now built from structure)
                            if (isset($col['structure_id'])) {
                                $column_id = $col['structure_id'];
                                $is_hidden = !$col['visible'];
                                // Ensure $col_fields is a flat array of field names
                                $col_fields = is_array($col['fields']) ? $col['fields'] : [$col['fields']];
                                // Flatten nested arrays if they exist
                                $col_fields = array_merge(...array_map(fn($f) => is_array($f) ? $f : [$f], $col_fields));
                                $col_field = $col['field'];
                            } else {
                                // Fallback for non-structure columns
                                $column_id = 'col_' . $loop->index . '_' . md5($col['label']);
                                $is_hidden = isset($column_preferences['hidden']) && in_array($column_id, $column_preferences['hidden']);
                                $col_fields = [$col['field']];
                                $col_field = $col['field'];
                            }
                        @endphp
                        @if(!$is_hidden)
                            @if ($col['replacements'])
                                @php $item[$col_field] = str_replace(array_keys($col['replacements']),$col['replacements'],$item[$col_field]) @endphp
                            @endif
                            <td class="{{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell' }}"
                                data-column-field="{{ $col_field }}">
                                <div class="flex items-center gap-2">
                                    <!-- Field Content -->
                                    <div class="flex-1">
                                        @if (isset($col['content']))
                                            @if (is_callable($col['content']))
                                                {!! $col['content']($item,$loop->first) !!}
                                            @else
                                                {!! $col['content'] ?? 'content' !!}
                                            @endif
                                        @else
                                            @if (is_array($col_fields))
                                                @php
                                                    // Safely handle potentially nested arrays
                                                    $field_values = [];
                                                    foreach ($col_fields as $field) {
                                                        if (is_array($field)) {
                                                            // Handle nested array
                                                            foreach ($field as $nested_field) {
                                                                if (isset($item[$nested_field])) {
                                                                    $field_values[] = $item[$nested_field];
                                                                }
                                                            }
                                                        } else {
                                                            // Handle regular field
                                                            if (isset($item[$field])) {
                                                                $field_values[] = $item[$field];
                                                            }
                                                        }
                                                    }
                                                    echo implode('<br>', $field_values);
                                                @endphp
                                            @else
                                                {{ $item[$col_field] ?? '' }}
                                            @endif
                                        @endif
                                    </div>

                                    <!-- Action Buttons -->
                                    @if (!empty($col['action_buttons']))
                                        <div class="flex items-center gap-1">
                                            @foreach ($col['action_buttons'] as $action)
                                                @php
                                                    $field_value = $item[$action['field']] ?? '';
                                                    $icon_display = match($action['icon']) {
                                                        'edit' => '✏️',
                                                        'delete' => '🗑️',
                                                        'view' => '👁️',
                                                        'download' => '⬇️',
                                                        'upload' => '⬆️',
                                                        'copy' => '📋',
                                                        'share' => '🔗',
                                                        'settings' => '⚙️',
                                                        'info' => 'ℹ️',
                                                        'warning' => '⚠️',
                                                        'success' => '✅',
                                                        'error' => '❌',
                                                        default => '🔘'
                                                    };
                                                @endphp
                                                <x-{{ $action['template'] }}
                                                        :field_value="$field_value"
                                                :item="$item"
                                                :icon="$icon_display"
                                                />
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </td>
                        @endif
                    @endforeach
                </tr>
                @if ($items_per_page > 1 && $loop->iteration > $items_per_page )
                    @break
                @endif
            @endforeach
            @endif {{-- End of @if (empty($items)) @else --}}
            @if (!$just_rows)
            </tbody>
        @endif
        @if (!$just_body && !$just_rows)
            <tfoot>
            <tr>
                <td colspan="{{ count($columns) > 0 ? count($columns) : 1 }}">
                    <x-pagination-strip
                            :item_count="isset($total_count) ? $total_count : count($items)"
                            :items_per_page="$items_per_page"
                            :current_page_num="$current_page_num"
                            :first_item="($current_page_num - 1) * $items_per_page"
                    ></x-pagination-strip>
                </td>
            </tr>
            </tfoot>
        </table>
    @endif {{-- End of @if (empty($columns)) @else --}}
@endif

