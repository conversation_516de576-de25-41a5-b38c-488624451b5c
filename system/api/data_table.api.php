<?php

namespace api\data_table;

use data_table\data_table;
use system\database;
use edge\edge;

// Include enhanced data callback functions if they exist

// Include logs callback functions if they exist
if (file_exists(FS_SYS_FUNCTIONS . DS . 'logs.fn.php')) {
    require_once FS_SYS_FUNCTIONS . DS . 'logs.fn.php';
}

function data_table_filter($p)
{
    $criteria = data_table::api_process_criteria($p);
    if (isset($p['callback']) && function_exists($p['callback'])) {
        print_rr("calling " . $p['callback'] . " criteria: " . print_r($criteria, true));
        return $p['callback'](criteria: $criteria);
    }
    print_rr("no callback found for " . $p['callback'] . " criteria: " . print_r($criteria, true));

    // If no callback, try to load data from data source
    if (isset($p['data_source_id']) && !empty($p['data_source_id'])) {
        // Direct data source access - use data_table_storage for configuration
        $data_source_id = (int)$p['data_source_id'];
        $table_name = $p['table_name'] ?? 'data_source_' . $data_source_id;
        $user_id = $_SESSION['user_id'] ?? null;

        // Try to get data using data_table_storage which handles both configuration and data
        $data_result = \system\data_table_storage::get_table_data($table_name, [], $criteria, $user_id, $data_source_id);

        if ($data_result['success'] && $data_result['source'] === 'data_source') {
            // We have data from data source via data_table_storage
            $items = $data_result['data'];
            $columns = $data_result['columns'] ?? [];
            $available_fields = [];

            // Get configuration to determine column structure
            $config = \system\data_table_storage::get_configuration($table_name, $user_id, $data_source_id);
            if ($config && isset($config['configuration']['structure']) && !empty($config['configuration']['structure'])) {
                // Use configured column structure
                $columns = [];
                foreach ($config['configuration']['structure'] as $col_config) {
                    if ($col_config['visible'] ?? true) {
                        $columns[] = [
                            'label' => $col_config['label'],
                            'field' => $col_config['field'],
                            'filter' => $col_config['filter'] ?? false,
                            'extra_parameters' => ''
                        ];
                        $available_fields[] = $col_config['field'];
                    }
                }
            } else {
                // Auto-generate columns from data if no configuration
                if (!empty($items)) {
                    $first_item = reset($items);
                    foreach (array_keys($first_item) as $field) {
                        // Skip system columns
                        if (in_array($field, ['id', 'data_hash'])) {
                            continue;
                        }
                        $columns[] = [
                            'label' => ucwords(str_replace('_', ' ', $field)),
                            'field' => $field,
                            'filter' => false,
                            'extra_parameters' => ''
                        ];
                        $available_fields[] = $field;
                    }
                }
            }


            // Process existing preferences to match new structure
            $column_structure = $column_preferences['structure'] ?? [];
            $hidden_columns = $column_preferences['hidden'] ?? [];

            // Debug removed for production

            // Use structure from preferences if it exists, otherwise generate from original columns
            if (!empty($column_structure)) {
                $processed_columns = $column_structure;
            } else {
                // Generate unique column IDs and prepare column structure
                $processed_columns = [];
                foreach ($columns as $index => $col) {
                    $column_id = 'col_' . $index . '_' . md5($col['label']);
                    $processed_columns[] = [
                        'id' => $column_id,
                        'label' => $col['label'],
                        'field' => $col['field'],
                        'filter' => $col['filter'] ?? false,
                        'fields' => is_array($col['field']) ? $col['field'] : [$col['field']], // Always array for consistency
                        'visible' => !in_array($column_id, $hidden_columns)
                    ];
                }

                // Note: We don't automatically save the configuration here anymore to prevent
                // overwriting data source settings during table regeneration. Configuration
                // is only saved when there are explicit user actions (column changes, data source changes, etc.)
            }

            // Use the data source settings passed as props (these are already determined in the parent component)
            tcs_log("Column manager props: table=$table_name, type=$current_data_source_type, id=$current_data_source_id", 'data_table_saga');
            print_rr([
                'data_table_column_manager_start' => [
                    'table_name' => $table_name,
                    'available_fields' => $available_fields,
                    'current_data_source_type' => $current_data_source_type,
                    'current_data_source_id' => $current_data_source_id
                ]
            ], 'data_table_column_manager_start');
            // Get available data sources (cached to avoid repeated queries)
            static $cached_data_sources = null;
            if ($cached_data_sources === null) {
                $available_data_sources = [];
                try {
                    $data_sources = database::table('autobooks_data_sources')
                        ->where('status', '=', 'active')
                        ->get();

                    foreach ($data_sources as $source) {
                        $available_data_sources[] = [
                            'id' => $source['id'],
                            'name' => $source['name'],
                            'description' => $source['description'],
                            'category' => $source['category'] ?? 'other'
                        ];
                    }
                    $cached_data_sources = $available_data_sources;
                } catch (Exception $e) {
                    // Handle error gracefully
                    $cached_data_sources = [];
                }
            } else {
                $available_data_sources = $cached_data_sources;
            }

            // Cache database column lookups to avoid repeated queries
            static $cached_db_columns = [];
            $cache_key = $db_table ?: 'empty';

            if (!empty($available_fields)) {
                $available_field_list = $available_fields;
            } else if (!empty($db_table)) {
                if (!isset($cached_db_columns[$cache_key])) {
                    $cached_db_columns[$cache_key] = database::table($db_table)->getColumns();
                }
                $available_field_list = $cached_db_columns[$cache_key];
            } else {
                $available_field_list = [];
            }

            $column_string = count($available_field_list) > 0 ? '["' . implode('","', $available_field_list) . '"]' : '[]';


            return '<div id="column_manager_panel_' . $table_name . '" hx-swap-oob="innerHTML">' . edge::render('data-table-column-manager-panel', [
                    'table_name' => $table_name,
                    'current_data_source_type' => 'data_source',
                    'current_data_source_id' => $data_source_id,
                    'available_data_sources'=>$available_data_sources,
                    'processed_columns' => $processed_columns,
                    'hidden_columns' => $hidden_columns,
                    'available_field_list' => $available_field_list
                ]) . '</div>' .
                edge::render('data-table', [
                    'table_name' => $table_name,
                    'items' => $items,
                    'columns' => $columns,
                    'just_table' => true,
                    'available_fields' => $available_fields,
                    'just_body' => $p['just_body'] ?? false,
                    'data_source_id' => $data_source_id,
                    'data_source_type' => 'data_source'
                ]);


        }

        // Fallback to direct data source access if data_table_storage fails

        // Before getting data, ensure the data source has correct search columns
        $data_source = \system\data_source_manager::get_data_source($data_source_id);
        if ($data_source && empty($data_source['search_columns'])) {
            // Data source exists but has no search columns configured
            $table_name = $data_source['table_name'];
            $table_info = \system\data_source_manager::get_table_info($table_name);

            if ($table_info && isset($table_info['columns'])) {
                $searchable_columns = [];
                foreach ($table_info['columns'] as $column) {
                    $column_name = $column['Field']; // DESCRIBE returns 'Field' not 'name'
                    $column_type = strtolower($column['Type']);

                    // Skip system columns
                    if (in_array($column_name, ['id', 'created_at', 'updated_at', 'data_hash'])) {
                        continue;
                    }

                    // Include text-based columns for search
                    if (strpos($column_type, 'varchar') !== false ||
                        strpos($column_type, 'text') !== false ||
                        strpos($column_type, 'char') !== false) {
                        $searchable_columns[] = $table_name . '.' . $column_name;
                    }
                }

                if (!empty($searchable_columns)) {
                    // Update the data source with search columns
                    try {
                        // Add search_columns field if it doesn't exist
                        $check_column_sql = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources' AND COLUMN_NAME = 'search_columns'";
                        $stmt = database::rawQuery($check_column_sql);
                        $column_exists = $stmt->fetchColumn();

                        if (!$column_exists) {
                            $add_column_sql = "ALTER TABLE `autobooks_data_sources` ADD COLUMN `search_columns` longtext DEFAULT NULL COMMENT 'JSON array of searchable column names for full-text search'";
                            database::rawQuery($add_column_sql);
                        }

                        // Update the data source
                        $update_sql = "UPDATE `autobooks_data_sources` SET `search_columns` = ? WHERE `id` = ?";
                        database::rawQuery($update_sql, [json_encode($searchable_columns), $data_source_id]);

                        tcs_log("Updated data source $data_source_id with search columns: " . json_encode($searchable_columns), 'data_table_saga');
                    } catch (Exception $e) {
                        tcs_log("Error updating data source search columns: " . $e->getMessage(), 'data_table_saga');
                    }
                }
            }
        }

        $data_result = \system\data_source_manager::get_data_source_data($data_source_id, $criteria);

        if ($data_result['success']) {
            $items = $data_result['data'];
            $columns = [];
            $available_fields = [];

            // Auto-generate columns from data if we have data
            if (!empty($items)) {
                $first_item = reset($items);
                foreach (array_keys($first_item) as $field) {
                    // Skip system columns
                    if (in_array($field, ['id', 'data_hash'])) {
                        continue;
                    }

                    $columns[] = [
                        'label' => ucwords(str_replace('_', ' ', $field)),
                        'field' => $field,
                        'filter' => false,
                        'extra_parameters' => ''
                    ];
                    $available_fields[] = $field;
                }
            } else {
                // No data returned - generate columns from data source configuration or table schema
                $data_source = \system\data_source_manager::get_data_source($data_source_id);
                if ($data_source && !empty($data_source['table_name'])) {
                    $table_info = \system\data_source_manager::get_table_info($data_source['table_name']);

                    if ($table_info && isset($table_info['columns'])) {
                        foreach ($table_info['columns'] as $column) {
                            $column_name = $column['Field']; // DESCRIBE returns 'Field' not 'name'

                            // Skip system columns
                            if (in_array($column_name, ['id', 'created_at', 'updated_at', 'data_hash'])) {
                                continue;
                            }

                            $columns[] = [
                                'label' => ucwords(str_replace('_', ' ', $column_name)),
                                'field' => $column_name,
                                'filter' => false,
                                'extra_parameters' => ''
                            ];
                            $available_fields[] = $column_name;
                        }
                    }
                }
            }

            return edge::render('data-table', [
                'table_name' => $p['table_name'] ?? 'data_source_' . $data_source_id,
                'items' => $items,
                'columns' => $columns,
                'available_fields' => $available_fields,
                'just_body' => $p['just_body'] ?? false,
                'data_source_id' => $data_source_id,
                'data_source_type' => 'data_source'
            ]);
        }
    } elseif (isset($p['table_name'])) {
        $user_id = $_SESSION['user_id'] ?? null;
        $data_result = \data_table_storage::get_table_data($p['table_name'], [], $criteria, $user_id);

        if ($data_result['success']) {
            $items = $data_result['data'];
            $columns = $data_result['columns'] ?? [];
            $available_fields = [];

            // Auto-generate columns from data if we have data
            if (!empty($items)) {
                $first_item = reset($items);
                foreach (array_keys($first_item) as $field) {
                    $columns[] = [
                        'label' => ucwords(str_replace('_', ' ', $field)),
                        'field' => $field,
                        'filter' => false,
                        'extra_parameters' => ''
                    ];
                    $available_fields[] = $field;
                }
            }

            return edge::render('data-table', [
                'table_name' => $p['table_name'],
                'items' => $items,
                'columns' => $columns,
                'available_fields' => $available_fields,
                'just_body' => $p['just_body'] ?? false
            ]);
        }
    }

    return edge::render('data-table', ['table_name' => $p['table_name'] ?? '']);
}

/**
 * Handle data table pagination requests
 * This function processes pagination requests from data tables
 *
 * @param array $p Parameters from the request including page, callback, etc.
 * @return string HTML content of the updated data table
 */
function pagination($p)
{
    // Extract pagination parameters
    $page = (int)($p['page'] ?? 1);
    $per_page = (int)($p['per_page'] ?? 30);
    $callback = $p['callback'] ?? '';
    $table_name = $p['table_name'] ?? '';

    // Build criteria for the callback function
    $criteria = [
        'limit' => $per_page,
        'offset' => ($page - 1) * $per_page,
        'page' => $page,
        'pagination_mode' => true  // Flag to indicate this is a pagination request
    ];

    // Add search terms if provided
    if (isset($p['search_terms']) && !empty($p['search_terms'])) {
        $criteria['search'] = $p['search_terms'];
    }

    // Add sorting if provided
    if (isset($p['sort_column']) && !empty($p['sort_column'])) {
        $criteria['order_by'] = $p['sort_column'];
        $criteria['order_direction'] = $p['sort_direction'] ?? 'asc';
    }

    // Call the appropriate callback function
    if (!empty($callback) && function_exists($callback)) {
        return $callback($criteria);
    } elseif (!empty($table_name)) {
        // Use enhanced data API for table-based requests
        return \api\enhanced_data\get_table_data(array_merge($p, [
            'page' => $page,
            'per_page' => $per_page,
            'just_body' => true
        ]));
    }

    return '<div class="error">Invalid pagination request</div>';
}