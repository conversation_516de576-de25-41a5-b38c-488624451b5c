[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 10:10:19
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69477960142838\n            [quantity] => 1\n            [endDate] => 2026-09-14\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-20T09:45:10.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T10:10:17.000Z\n    [csn] => 5103159758\n)\n
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14' ON DUPLICATE KEY UPDATE subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-08-20 10:10:19
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69477960142838\n            [quantity] => 1\n            [endDate] => 2026-09-14\n            [message] => subscription quantity,endDate changed.\n            [modifiedAt] => 2025-08-20T09:45:10.000+0000\n        )\n\n    [publishedAt] => 2025-08-20T10:10:17.000Z\n    [csn] => 5103159758\n)\n
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-08-20 10:10:19] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14' ON DUPLICATE KEY UPDATE subscriptionId = '69477960142838', quantity = 1, endDate = '2026-09-14';\n",\n        "affected_rows": 0\n    }\n]
