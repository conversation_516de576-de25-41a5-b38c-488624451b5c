[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 09:45:08
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:36]  Provided signature: sha256=11ee8a292e6780479c0b7ed9015e13afb9a6c256d2c59b0d77c5e3ff4cd08d15
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:37]  Calculated signature: sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8d56f773a4368a8e\n    [X-B3-Traceid] => 68a59921d6ef70cea18754091004f23f\n    [B3] => 68a59921d6ef70cea18754091004f23f-8d56f773a4368a8e-1\n    [Traceparent] => 00-68a59921d6ef70cea18754091004f23f-8d56f773a4368a8e-01\n    [X-Amzn-Trace-Id] => Root=1-68a59921-d6ef70cea18754091004f23f;Parent=8d56f773a4368a8e;Sampled=1\n    [X-Adsk-Signature] => sha256=11ee8a292e6780479c0b7ed9015e13afb9a6c256d2c59b0d77c5e3ff4cd08d15\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Ordered","message":"Quote# Q-995219 status changed to Ordered.","modifiedAt":"2025-08-20T09:45:05.184Z"},"publishedAt":"2025-08-20T09:45:05.000Z","csn":"5103159758"}
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 09:45:08
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:36]  Provided signature: sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:37]  Calculated signature: sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 017c3ac81c6cfa56\n    [X-B3-Traceid] => 68a59921d6ef70cea18754091004f23f\n    [B3] => 68a59921d6ef70cea18754091004f23f-017c3ac81c6cfa56-1\n    [Traceparent] => 00-68a59921d6ef70cea18754091004f23f-017c3ac81c6cfa56-01\n    [X-Amzn-Trace-Id] => Root=1-68a59921-d6ef70cea18754091004f23f;Parent=017c3ac81c6cfa56;Sampled=1\n    [X-Adsk-Signature] => sha256=77b887d305529f7cba7c0f0209f9b40fedabde7724e7943e916b809ca8f4ce18\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 09:45:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Ordered","message":"Quote# Q-995219 status changed to Ordered.","modifiedAt":"2025-08-20T09:45:05.184Z"},"publishedAt":"2025-08-20T09:45:05.000Z","csn":"5103159758"}
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 10:10:19
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:36]  Provided signature: sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:37]  Calculated signature: sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7acd0a11cdd4e537\n    [X-B3-Traceid] => 68a59f09043f834e109e9d755d22a94d\n    [B3] => 68a59f09043f834e109e9d755d22a94d-7acd0a11cdd4e537-1\n    [Traceparent] => 00-68a59f09043f834e109e9d755d22a94d-7acd0a11cdd4e537-01\n    [X-Amzn-Trace-Id] => Root=1-68a59f09-043f834e109e9d755d22a94d;Parent=7acd0a11cdd4e537;Sampled=1\n    [X-Adsk-Signature] => sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"a7f76573-ab85-494e-bf70-3088e81a056f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","quantity":1,"endDate":"2026-09-14","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-20T09:45:10.000+0000"},"publishedAt":"2025-08-20T10:10:17.000Z","csn":"5103159758"}
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-20 10:10:19
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:36]  Provided signature: sha256=b7bbe4ad66adbfc9ac2da46b95571306927d611e15ef7ee30c6245eea6839fb6
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:37]  Calculated signature: sha256=8c6440615f372c63cf8da644f959e68458cef710cf4808b4bcd8b8cc85b5c93a
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cb8e73c716d57d9d\n    [X-B3-Traceid] => 68a59f09043f834e109e9d755d22a94d\n    [B3] => 68a59f09043f834e109e9d755d22a94d-cb8e73c716d57d9d-1\n    [Traceparent] => 00-68a59f09043f834e109e9d755d22a94d-cb8e73c716d57d9d-01\n    [X-Amzn-Trace-Id] => Root=1-68a59f09-043f834e109e9d755d22a94d;Parent=cb8e73c716d57d9d;Sampled=1\n    [X-Adsk-Signature] => sha256=b7bbe4ad66adbfc9ac2da46b95571306927d611e15ef7ee30c6245eea6839fb6\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => a7f76573-ab85-494e-bf70-3088e81a056f\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-20 10:10:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"a7f76573-ab85-494e-bf70-3088e81a056f","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69477960142838","quantity":1,"endDate":"2026-09-14","message":"subscription quantity,endDate changed.","modifiedAt":"2025-08-20T09:45:10.000+0000"},"publishedAt":"2025-08-20T10:10:17.000Z","csn":"5103159758"}
