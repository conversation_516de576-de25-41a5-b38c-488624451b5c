[navigation] [2025-08-20 09:06:33] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755680793.csv
[navigation] [2025-08-20 09:06:34] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755680793.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 09:39:38] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketechup_csv_file_1755682778.csv
[navigation] [2025-08-20 09:39:39] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketechup_csv_file_1755682778.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketechup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 09:43:45] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755683025.csv
[navigation] [2025-08-20 09:43:46] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755683025.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 09:43:47] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-20 09:43:47] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-20 09:52:00] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-20 09:52:28] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755683548.csv
[navigation] [2025-08-20 09:52:29] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755683548.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 09:58:54] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755683934.csv
[navigation] [2025-08-20 09:58:55] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755683934.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 10:01:12] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755684072.csv
[navigation] [2025-08-20 10:01:13] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755684072.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 10:01:14] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-20 10:01:14] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-20 10:02:39] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-20 10:03:31] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755684211.csv
[navigation] [2025-08-20 10:03:32] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755684211.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 10:03:33] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-20 10:03:33] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-20 10:53:25] [nav_tree.api.php:1012] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [data_source_delete] => 1\n    [storage_delete] => 1\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-20 10:53:47] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755687227.csv
[navigation] [2025-08-20 10:53:48] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1755687227.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-20 10:53:49] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-20 10:53:49] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
