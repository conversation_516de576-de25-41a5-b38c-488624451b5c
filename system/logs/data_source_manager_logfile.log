[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.id => sketchup_id AND sketchup.id => sketchup_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.sold_to_name => company_name AND sketchup.sold_to_name => company_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.sold_to_number => sketchup_sold_to_number AND sketchup.sold_to_number => sketchup_sold_to_number
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: vendor_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for vendor_name: found 3 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback vendor_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1539] Fallback success: vendor_name -> contact_name (score: 56.5, confidence: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.vendor_name => contact_name AND sketchup.vendor_name => contact_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.reseller_number => reseller_name AND sketchup.reseller_number => reseller_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: reseller_vendor_id -> reseller_name (score: 54.5) loses to reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for reseller_vendor_id: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback reseller_vendor_id -> reseller_name: score 54.5 loses to existing reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for reseller_vendor_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.reseller_vendor_id => sketchup_reseller_vendor_id AND sketchup.reseller_vendor_id => sketchup_reseller_vendor_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_vendor_id => sketchup_end_customer_vendor_id AND sketchup.end_customer_vendor_id => sketchup_end_customer_vendor_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: end_customer_name -> company_name (score: 67.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for end_customer_name: found 2 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback end_customer_name -> company_name: score 65.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback end_customer_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_name => sketchup_end_customer_name AND sketchup.end_customer_name => sketchup_end_customer_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_1 => address AND sketchup.end_customer_address_1 => address
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_2 => sketchup_end_customer_address_2 AND sketchup.end_customer_address_2 => sketchup_end_customer_address_2
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_3 => sketchup_end_customer_address_3 AND sketchup.end_customer_address_3 => sketchup_end_customer_address_3
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_city => city AND sketchup.end_customer_city => city
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_state => state AND sketchup.end_customer_state => state
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_zip_code => postal_code AND sketchup.end_customer_zip_code => postal_code
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_country => country AND sketchup.end_customer_country => country
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_account_type => sketchup_end_customer_account_type AND sketchup.end_customer_account_type => sketchup_end_customer_account_type
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: end_customer_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for end_customer_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_contact_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_name => sketchup_end_customer_contact_name AND sketchup.end_customer_contact_name => sketchup_end_customer_contact_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_email => email AND sketchup.end_customer_contact_email => email
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_phone => sketchup_end_customer_contact_phone AND sketchup.end_customer_contact_phone => sketchup_end_customer_contact_phone
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_industry_segment => sketchup_end_customer_industry_segment AND sketchup.end_customer_industry_segment => sketchup_end_customer_industry_segment
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_program_name => product_name AND sketchup.agreement_program_name => product_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_number => subscription_reference AND sketchup.agreement_number => subscription_reference
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_start_date => start_date AND sketchup.agreement_start_date => start_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_end_date => end_date AND sketchup.agreement_end_date => end_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_terms => sketchup_agreement_terms AND sketchup.agreement_terms => sketchup_agreement_terms
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_type => sketchup_agreement_type AND sketchup.agreement_type => sketchup_agreement_type
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_status => status AND sketchup.agreement_status => status
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_support_level => sketchup_agreement_support_level AND sketchup.agreement_support_level => sketchup_agreement_support_level
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_days_due => sketchup_agreement_days_due AND sketchup.agreement_days_due => sketchup_agreement_days_due
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_autorenew => sketchup_agreement_autorenew AND sketchup.agreement_autorenew => sketchup_agreement_autorenew
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_name -> product_name (score: 70) loses to agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_name: found 3 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_name -> product_name: score 68 loses to existing agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_name => sketchup_product_name AND sketchup.product_name => sketchup_product_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:325] Alias conflict: product_family -> product_name (score: 79) replaces agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_family => product_name AND sketchup.product_family => product_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_market_segment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_market_segment: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_market_segment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_market_segment
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_market_segment => sketchup_product_market_segment AND sketchup.product_market_segment => sketchup_product_market_segment
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_release -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_release: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_release -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_release
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_release => sketchup_product_release AND sketchup.product_release => sketchup_product_release
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_type -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_type: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_type -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_type
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_type => sketchup_product_type AND sketchup.product_type => sketchup_product_type
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_deployment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_deployment: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_deployment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_deployment
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_deployment => sketchup_product_deployment AND sketchup.product_deployment => sketchup_product_deployment
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_sku -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_sku: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_sku -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_sku
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku => sketchup_product_sku AND sketchup.product_sku => sketchup_product_sku
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_sku_description -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_sku_description: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_sku_description -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_sku_description
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku_description => sketchup_product_sku_description AND sketchup.product_sku_description => sketchup_product_sku_description
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_part -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_part: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_part -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for product_part
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_part => sketchup_product_part AND sketchup.product_part => sketchup_product_part
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_list_price -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_list_price: found 2 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_list_price -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1539] Fallback success: product_list_price -> price (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price => price AND sketchup.product_list_price => price
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: product_list_price_currency -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for product_list_price_currency: found 3 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback product_list_price_currency -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1539] Fallback success: product_list_price_currency -> currency (score: 61, confidence: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price_currency => currency AND sketchup.product_list_price_currency => currency
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_id -> subscription_reference (score: 85) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_id: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_id -> subscription_reference: score 83 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for subscription_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_id => sketchup_subscription_id AND sketchup.subscription_id => sketchup_subscription_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_serial_number -> subscription_reference (score: 79) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_serial_number: found 2 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_serial_number -> subscription_reference: score 79 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1539] Fallback success: subscription_serial_number -> serial_number (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_serial_number => serial_number AND sketchup.subscription_serial_number => serial_number
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_status -> status (score: 58) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_status: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_status -> status: score 56 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for subscription_status
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_status => sketchup_subscription_status AND sketchup.subscription_status => sketchup_subscription_status
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_quantity => quantity AND sketchup.subscription_quantity => quantity
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_start_date -> start_date (score: 58) loses to agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_start_date: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_start_date -> start_date: score 56 loses to existing agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for subscription_start_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_start_date => sketchup_subscription_start_date AND sketchup.subscription_start_date => sketchup_subscription_start_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_end_date -> end_date (score: 58) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_end_date: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_end_date -> end_date: score 56 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for subscription_end_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_end_date => sketchup_subscription_end_date AND sketchup.subscription_end_date => sketchup_subscription_end_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_name => sketchup_subscription_contact_name AND sketchup.subscription_contact_name => sketchup_subscription_contact_name
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_email: found 2 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback subscription_contact_email -> email: score 82 loses to existing end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1521] Fallback subscription_contact_email -> contact_name: confidence 56 below threshold 60
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_email
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_email => sketchup_subscription_contact_email AND sketchup.subscription_contact_email => sketchup_subscription_contact_email
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_level => sketchup_subscription_level AND sketchup.subscription_level => sketchup_subscription_level
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_days_due => sketchup_subscription_days_due AND sketchup.subscription_days_due => sketchup_subscription_days_due
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_id => sketchup_quotation_id AND sketchup.quotation_id => sketchup_quotation_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_type => sketchup_quotation_type AND sketchup.quotation_type => sketchup_quotation_type
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_vendor_id => sketchup_quotation_vendor_id AND sketchup.quotation_vendor_id => sketchup_quotation_vendor_id
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_deal_registration_number => sketchup_quotation_deal_registration_number AND sketchup.quotation_deal_registration_number => sketchup_quotation_deal_registration_number
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: quotation_status -> status (score: 52) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for quotation_status: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback quotation_status -> status: score 52 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for quotation_status
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_status => sketchup_quotation_status AND sketchup.quotation_status => sketchup_quotation_status
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous AND sketchup.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:327] Alias conflict: quotation_due_date -> end_date (score: 52) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1511] Trying fallback for quotation_due_date: found 1 alternatives
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1534] Fallback quotation_due_date -> end_date: score 52 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:1544] No suitable fallback found for quotation_due_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_due_date => sketchup_quotation_due_date AND sketchup.quotation_due_date => sketchup_quotation_due_date
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.flaer_phase => sketchup_flaer_phase AND sketchup.flaer_phase => sketchup_flaer_phase
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated => sketchup_updated AND sketchup.updated => sketchup_updated
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.created_at => sketchup_created_at AND sketchup.created_at => sketchup_created_at
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated_at => sketchup_updated_at AND sketchup.updated_at => sketchup_updated_at
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:187] Generated 124 column aliases: {"autobooks_import_sketchup_data.id":"sketchup_id","sketchup.id":"sketchup_id","autobooks_import_sketchup_data.sold_to_name":"company_name","sketchup.sold_to_name":"company_name","autobooks_import_sketchup_data.sold_to_number":"sketchup_sold_to_number","sketchup.sold_to_number":"sketchup_sold_to_number","autobooks_import_sketchup_data.vendor_name":"contact_name","sketchup.vendor_name":"contact_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","sketchup.reseller_number":"reseller_name","autobooks_import_sketchup_data.reseller_vendor_id":"sketchup_reseller_vendor_id","sketchup.reseller_vendor_id":"sketchup_reseller_vendor_id","autobooks_import_sketchup_data.end_customer_vendor_id":"sketchup_end_customer_vendor_id","sketchup.end_customer_vendor_id":"sketchup_end_customer_vendor_id","autobooks_import_sketchup_data.end_customer_name":"sketchup_end_customer_name","sketchup.end_customer_name":"sketchup_end_customer_name","autobooks_import_sketchup_data.end_customer_address_1":"address","sketchup.end_customer_address_1":"address","autobooks_import_sketchup_data.end_customer_address_2":"sketchup_end_customer_address_2","sketchup.end_customer_address_2":"sketchup_end_customer_address_2","autobooks_import_sketchup_data.end_customer_address_3":"sketchup_end_customer_address_3","sketchup.end_customer_address_3":"sketchup_end_customer_address_3","autobooks_import_sketchup_data.end_customer_city":"city","sketchup.end_customer_city":"city","autobooks_import_sketchup_data.end_customer_state":"state","sketchup.end_customer_state":"state","autobooks_import_sketchup_data.end_customer_zip_code":"postal_code","sketchup.end_customer_zip_code":"postal_code","autobooks_import_sketchup_data.end_customer_country":"country","sketchup.end_customer_country":"country","autobooks_import_sketchup_data.end_customer_account_type":"sketchup_end_customer_account_type","sketchup.end_customer_account_type":"sketchup_end_customer_account_type","autobooks_import_sketchup_data.end_customer_contact_name":"sketchup_end_customer_contact_name","sketchup.end_customer_contact_name":"sketchup_end_customer_contact_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","sketchup.end_customer_contact_email":"email","autobooks_import_sketchup_data.end_customer_contact_phone":"sketchup_end_customer_contact_phone","sketchup.end_customer_contact_phone":"sketchup_end_customer_contact_phone","autobooks_import_sketchup_data.end_customer_industry_segment":"sketchup_end_customer_industry_segment","sketchup.end_customer_industry_segment":"sketchup_end_customer_industry_segment","autobooks_import_sketchup_data.agreement_program_name":"product_name","sketchup.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","sketchup.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"start_date","sketchup.agreement_start_date":"start_date","autobooks_import_sketchup_data.agreement_end_date":"end_date","sketchup.agreement_end_date":"end_date","autobooks_import_sketchup_data.agreement_terms":"sketchup_agreement_terms","sketchup.agreement_terms":"sketchup_agreement_terms","autobooks_import_sketchup_data.agreement_type":"sketchup_agreement_type","sketchup.agreement_type":"sketchup_agreement_type","autobooks_import_sketchup_data.agreement_status":"status","sketchup.agreement_status":"status","autobooks_import_sketchup_data.agreement_support_level":"sketchup_agreement_support_level","sketchup.agreement_support_level":"sketchup_agreement_support_level","autobooks_import_sketchup_data.agreement_days_due":"sketchup_agreement_days_due","sketchup.agreement_days_due":"sketchup_agreement_days_due","autobooks_import_sketchup_data.agreement_autorenew":"sketchup_agreement_autorenew","sketchup.agreement_autorenew":"sketchup_agreement_autorenew","autobooks_import_sketchup_data.product_name":"sketchup_product_name","sketchup.product_name":"sketchup_product_name","autobooks_import_sketchup_data.product_family":"product_name","sketchup.product_family":"product_name","autobooks_import_sketchup_data.product_market_segment":"sketchup_product_market_segment","sketchup.product_market_segment":"sketchup_product_market_segment","autobooks_import_sketchup_data.product_release":"sketchup_product_release","sketchup.product_release":"sketchup_product_release","autobooks_import_sketchup_data.product_type":"sketchup_product_type","sketchup.product_type":"sketchup_product_type","autobooks_import_sketchup_data.product_deployment":"sketchup_product_deployment","sketchup.product_deployment":"sketchup_product_deployment","autobooks_import_sketchup_data.product_sku":"sketchup_product_sku","sketchup.product_sku":"sketchup_product_sku","autobooks_import_sketchup_data.product_sku_description":"sketchup_product_sku_description","sketchup.product_sku_description":"sketchup_product_sku_description","autobooks_import_sketchup_data.product_part":"sketchup_product_part","sketchup.product_part":"sketchup_product_part","autobooks_import_sketchup_data.product_list_price":"price","sketchup.product_list_price":"price","autobooks_import_sketchup_data.product_list_price_currency":"currency","sketchup.product_list_price_currency":"currency","autobooks_import_sketchup_data.subscription_id":"sketchup_subscription_id","sketchup.subscription_id":"sketchup_subscription_id","autobooks_import_sketchup_data.subscription_serial_number":"serial_number","sketchup.subscription_serial_number":"serial_number","autobooks_import_sketchup_data.subscription_status":"sketchup_subscription_status","sketchup.subscription_status":"sketchup_subscription_status","autobooks_import_sketchup_data.subscription_quantity":"quantity","sketchup.subscription_quantity":"quantity","autobooks_import_sketchup_data.subscription_start_date":"sketchup_subscription_start_date","sketchup.subscription_start_date":"sketchup_subscription_start_date","autobooks_import_sketchup_data.subscription_end_date":"sketchup_subscription_end_date","sketchup.subscription_end_date":"sketchup_subscription_end_date","autobooks_import_sketchup_data.subscription_contact_name":"sketchup_subscription_contact_name","sketchup.subscription_contact_name":"sketchup_subscription_contact_name","autobooks_import_sketchup_data.subscription_contact_email":"sketchup_subscription_contact_email","sketchup.subscription_contact_email":"sketchup_subscription_contact_email","autobooks_import_sketchup_data.subscription_level":"sketchup_subscription_level","sketchup.subscription_level":"sketchup_subscription_level","autobooks_import_sketchup_data.subscription_days_due":"sketchup_subscription_days_due","sketchup.subscription_days_due":"sketchup_subscription_days_due","autobooks_import_sketchup_data.quotation_id":"sketchup_quotation_id","sketchup.quotation_id":"sketchup_quotation_id","autobooks_import_sketchup_data.quotation_type":"sketchup_quotation_type","sketchup.quotation_type":"sketchup_quotation_type","autobooks_import_sketchup_data.quotation_vendor_id":"sketchup_quotation_vendor_id","sketchup.quotation_vendor_id":"sketchup_quotation_vendor_id","autobooks_import_sketchup_data.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","sketchup.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","autobooks_import_sketchup_data.quotation_status":"sketchup_quotation_status","sketchup.quotation_status":"sketchup_quotation_status","autobooks_import_sketchup_data.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","sketchup.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","autobooks_import_sketchup_data.quotation_due_date":"sketchup_quotation_due_date","sketchup.quotation_due_date":"sketchup_quotation_due_date","autobooks_import_sketchup_data.flaer_phase":"sketchup_flaer_phase","sketchup.flaer_phase":"sketchup_flaer_phase","autobooks_import_sketchup_data.updated":"sketchup_updated","sketchup.updated":"sketchup_updated","autobooks_import_sketchup_data.created_at":"sketchup_created_at","sketchup.created_at":"sketchup_created_at","autobooks_import_sketchup_data.updated_at":"sketchup_updated_at","sketchup.updated_at":"sketchup_updated_at"}
[data_source_manager] [2025-08-20 09:43:47] [data_source_manager.class.php:267] Created data source: CSV Import: Sketchup with ID: 73
[data_source_manager] [2025-08-20 09:46:06] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-20 09:46:06] [data_source_manager.class.php:640] Executing query: SELECT `subs`.`id` AS `subs_id`, `subs`.`subscriptionId` AS `subs_subscriptionId`, `subs`.`subscriptionReferenceNumber` AS `subs_subscriptionReferenceNumber`, `subs`.`quantity` AS `subs_quantity`, `subs`.`status` AS `subs_status`, `subs`.`startDate` AS `subs_startDate`, `subs`.`endDate` AS `subs_endDate`, `endcust`.`id` AS `endcust_id`, `endcust`.`account_csn` AS `endcust_account_csn`, `endcust`.`name` AS `endcust_name`, `endcust`.`first_name` AS `endcust_first_name`, `endcust`.`last_name` AS `endcust_last_name`, `lastquote`.`quote_id` AS `lastquote_quote_id`, `lastquote`.`quote_number` AS `lastquote_quote_number`, `lastquote`.`quoted_date` AS `lastquote_quoted_date`, (CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END) AS `subs_enddatediff` FROM `autodesk_subscriptions` AS `subs` LEFT JOIN `autodesk_accounts` AS `endcust` ON `subs`.`endCustomer_csn` = `endcust`.`account_csn` LEFT JOIN (SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\n\nFROM autodesk_quote_line_items qi\nJOIN autodesk_quotes q ON q.id = qi.quote_id\n\nWHERE qi.subscription_id IS NOT NULL\n  AND q.quote_status NOT IN ('Expired', 'Cancelled')\n\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\n\nLIMIT 1) AS `lastquote` ON subs.subscriptionId = lastquote.subscription_id ORDER BY `subs_enddatediff` ASC
[data_source_manager] [2025-08-20 09:51:43] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 09:51:43] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-08-20 09:51:46] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 09:51:46] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.id => sketchup_id AND sketchup.id => sketchup_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.sold_to_name => company_name AND sketchup.sold_to_name => company_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.sold_to_number => sketchup_sold_to_number AND sketchup.sold_to_number => sketchup_sold_to_number
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: vendor_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for vendor_name: found 3 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback vendor_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1539] Fallback success: vendor_name -> contact_name (score: 56.5, confidence: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.vendor_name => contact_name AND sketchup.vendor_name => contact_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.reseller_number => reseller_name AND sketchup.reseller_number => reseller_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: reseller_vendor_id -> reseller_name (score: 54.5) loses to reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for reseller_vendor_id: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback reseller_vendor_id -> reseller_name: score 54.5 loses to existing reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for reseller_vendor_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.reseller_vendor_id => sketchup_reseller_vendor_id AND sketchup.reseller_vendor_id => sketchup_reseller_vendor_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_vendor_id => sketchup_end_customer_vendor_id AND sketchup.end_customer_vendor_id => sketchup_end_customer_vendor_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: end_customer_name -> company_name (score: 67.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for end_customer_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback end_customer_name -> company_name: score 65.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback end_customer_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_name => sketchup_end_customer_name AND sketchup.end_customer_name => sketchup_end_customer_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_1 => address AND sketchup.end_customer_address_1 => address
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_2 => sketchup_end_customer_address_2 AND sketchup.end_customer_address_2 => sketchup_end_customer_address_2
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_3 => sketchup_end_customer_address_3 AND sketchup.end_customer_address_3 => sketchup_end_customer_address_3
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_city => city AND sketchup.end_customer_city => city
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_state => state AND sketchup.end_customer_state => state
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_zip_code => postal_code AND sketchup.end_customer_zip_code => postal_code
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_country => country AND sketchup.end_customer_country => country
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_account_type => sketchup_end_customer_account_type AND sketchup.end_customer_account_type => sketchup_end_customer_account_type
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: end_customer_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for end_customer_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_contact_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_name => sketchup_end_customer_contact_name AND sketchup.end_customer_contact_name => sketchup_end_customer_contact_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_email => email AND sketchup.end_customer_contact_email => email
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_phone => sketchup_end_customer_contact_phone AND sketchup.end_customer_contact_phone => sketchup_end_customer_contact_phone
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_industry_segment => sketchup_end_customer_industry_segment AND sketchup.end_customer_industry_segment => sketchup_end_customer_industry_segment
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_program_name => product_name AND sketchup.agreement_program_name => product_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_number => subscription_reference AND sketchup.agreement_number => subscription_reference
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_start_date => start_date AND sketchup.agreement_start_date => start_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_end_date => end_date AND sketchup.agreement_end_date => end_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_terms => sketchup_agreement_terms AND sketchup.agreement_terms => sketchup_agreement_terms
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_type => sketchup_agreement_type AND sketchup.agreement_type => sketchup_agreement_type
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_status => status AND sketchup.agreement_status => status
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_support_level => sketchup_agreement_support_level AND sketchup.agreement_support_level => sketchup_agreement_support_level
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_days_due => sketchup_agreement_days_due AND sketchup.agreement_days_due => sketchup_agreement_days_due
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_autorenew => sketchup_agreement_autorenew AND sketchup.agreement_autorenew => sketchup_agreement_autorenew
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_name -> product_name (score: 70) loses to agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_name: found 3 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_name -> product_name: score 68 loses to existing agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_name => sketchup_product_name AND sketchup.product_name => sketchup_product_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:325] Alias conflict: product_family -> product_name (score: 79) replaces agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_family => product_name AND sketchup.product_family => product_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_market_segment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_market_segment: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_market_segment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_market_segment
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_market_segment => sketchup_product_market_segment AND sketchup.product_market_segment => sketchup_product_market_segment
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_release -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_release: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_release -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_release
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_release => sketchup_product_release AND sketchup.product_release => sketchup_product_release
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_type -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_type: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_type -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_type
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_type => sketchup_product_type AND sketchup.product_type => sketchup_product_type
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_deployment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_deployment: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_deployment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_deployment
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_deployment => sketchup_product_deployment AND sketchup.product_deployment => sketchup_product_deployment
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_sku -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_sku: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_sku -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_sku
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku => sketchup_product_sku AND sketchup.product_sku => sketchup_product_sku
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_sku_description -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_sku_description: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_sku_description -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_sku_description
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku_description => sketchup_product_sku_description AND sketchup.product_sku_description => sketchup_product_sku_description
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_part -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_part: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_part -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for product_part
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_part => sketchup_product_part AND sketchup.product_part => sketchup_product_part
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_list_price -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_list_price: found 2 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_list_price -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1539] Fallback success: product_list_price -> price (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price => price AND sketchup.product_list_price => price
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: product_list_price_currency -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for product_list_price_currency: found 3 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback product_list_price_currency -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1539] Fallback success: product_list_price_currency -> currency (score: 61, confidence: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price_currency => currency AND sketchup.product_list_price_currency => currency
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_id -> subscription_reference (score: 85) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_id: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_id -> subscription_reference: score 83 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for subscription_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_id => sketchup_subscription_id AND sketchup.subscription_id => sketchup_subscription_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_serial_number -> subscription_reference (score: 79) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_serial_number: found 2 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_serial_number -> subscription_reference: score 79 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1539] Fallback success: subscription_serial_number -> serial_number (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_serial_number => serial_number AND sketchup.subscription_serial_number => serial_number
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_status -> status (score: 58) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_status: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_status -> status: score 56 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for subscription_status
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_status => sketchup_subscription_status AND sketchup.subscription_status => sketchup_subscription_status
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_quantity => quantity AND sketchup.subscription_quantity => quantity
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_start_date -> start_date (score: 58) loses to agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_start_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_start_date -> start_date: score 56 loses to existing agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for subscription_start_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_start_date => sketchup_subscription_start_date AND sketchup.subscription_start_date => sketchup_subscription_start_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_end_date -> end_date (score: 58) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_end_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_end_date -> end_date: score 56 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for subscription_end_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_end_date => sketchup_subscription_end_date AND sketchup.subscription_end_date => sketchup_subscription_end_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_name => sketchup_subscription_contact_name AND sketchup.subscription_contact_name => sketchup_subscription_contact_name
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_email: found 2 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback subscription_contact_email -> email: score 82 loses to existing end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1521] Fallback subscription_contact_email -> contact_name: confidence 56 below threshold 60
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_email
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_email => sketchup_subscription_contact_email AND sketchup.subscription_contact_email => sketchup_subscription_contact_email
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_level => sketchup_subscription_level AND sketchup.subscription_level => sketchup_subscription_level
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_days_due => sketchup_subscription_days_due AND sketchup.subscription_days_due => sketchup_subscription_days_due
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_id => sketchup_quotation_id AND sketchup.quotation_id => sketchup_quotation_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_type => sketchup_quotation_type AND sketchup.quotation_type => sketchup_quotation_type
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_vendor_id => sketchup_quotation_vendor_id AND sketchup.quotation_vendor_id => sketchup_quotation_vendor_id
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_deal_registration_number => sketchup_quotation_deal_registration_number AND sketchup.quotation_deal_registration_number => sketchup_quotation_deal_registration_number
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: quotation_status -> status (score: 52) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for quotation_status: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback quotation_status -> status: score 52 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for quotation_status
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_status => sketchup_quotation_status AND sketchup.quotation_status => sketchup_quotation_status
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous AND sketchup.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:327] Alias conflict: quotation_due_date -> end_date (score: 52) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1511] Trying fallback for quotation_due_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1534] Fallback quotation_due_date -> end_date: score 52 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:1544] No suitable fallback found for quotation_due_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_due_date => sketchup_quotation_due_date AND sketchup.quotation_due_date => sketchup_quotation_due_date
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.flaer_phase => sketchup_flaer_phase AND sketchup.flaer_phase => sketchup_flaer_phase
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated => sketchup_updated AND sketchup.updated => sketchup_updated
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.created_at => sketchup_created_at AND sketchup.created_at => sketchup_created_at
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated_at => sketchup_updated_at AND sketchup.updated_at => sketchup_updated_at
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:187] Generated 124 column aliases: {"autobooks_import_sketchup_data.id":"sketchup_id","sketchup.id":"sketchup_id","autobooks_import_sketchup_data.sold_to_name":"company_name","sketchup.sold_to_name":"company_name","autobooks_import_sketchup_data.sold_to_number":"sketchup_sold_to_number","sketchup.sold_to_number":"sketchup_sold_to_number","autobooks_import_sketchup_data.vendor_name":"contact_name","sketchup.vendor_name":"contact_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","sketchup.reseller_number":"reseller_name","autobooks_import_sketchup_data.reseller_vendor_id":"sketchup_reseller_vendor_id","sketchup.reseller_vendor_id":"sketchup_reseller_vendor_id","autobooks_import_sketchup_data.end_customer_vendor_id":"sketchup_end_customer_vendor_id","sketchup.end_customer_vendor_id":"sketchup_end_customer_vendor_id","autobooks_import_sketchup_data.end_customer_name":"sketchup_end_customer_name","sketchup.end_customer_name":"sketchup_end_customer_name","autobooks_import_sketchup_data.end_customer_address_1":"address","sketchup.end_customer_address_1":"address","autobooks_import_sketchup_data.end_customer_address_2":"sketchup_end_customer_address_2","sketchup.end_customer_address_2":"sketchup_end_customer_address_2","autobooks_import_sketchup_data.end_customer_address_3":"sketchup_end_customer_address_3","sketchup.end_customer_address_3":"sketchup_end_customer_address_3","autobooks_import_sketchup_data.end_customer_city":"city","sketchup.end_customer_city":"city","autobooks_import_sketchup_data.end_customer_state":"state","sketchup.end_customer_state":"state","autobooks_import_sketchup_data.end_customer_zip_code":"postal_code","sketchup.end_customer_zip_code":"postal_code","autobooks_import_sketchup_data.end_customer_country":"country","sketchup.end_customer_country":"country","autobooks_import_sketchup_data.end_customer_account_type":"sketchup_end_customer_account_type","sketchup.end_customer_account_type":"sketchup_end_customer_account_type","autobooks_import_sketchup_data.end_customer_contact_name":"sketchup_end_customer_contact_name","sketchup.end_customer_contact_name":"sketchup_end_customer_contact_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","sketchup.end_customer_contact_email":"email","autobooks_import_sketchup_data.end_customer_contact_phone":"sketchup_end_customer_contact_phone","sketchup.end_customer_contact_phone":"sketchup_end_customer_contact_phone","autobooks_import_sketchup_data.end_customer_industry_segment":"sketchup_end_customer_industry_segment","sketchup.end_customer_industry_segment":"sketchup_end_customer_industry_segment","autobooks_import_sketchup_data.agreement_program_name":"product_name","sketchup.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","sketchup.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"start_date","sketchup.agreement_start_date":"start_date","autobooks_import_sketchup_data.agreement_end_date":"end_date","sketchup.agreement_end_date":"end_date","autobooks_import_sketchup_data.agreement_terms":"sketchup_agreement_terms","sketchup.agreement_terms":"sketchup_agreement_terms","autobooks_import_sketchup_data.agreement_type":"sketchup_agreement_type","sketchup.agreement_type":"sketchup_agreement_type","autobooks_import_sketchup_data.agreement_status":"status","sketchup.agreement_status":"status","autobooks_import_sketchup_data.agreement_support_level":"sketchup_agreement_support_level","sketchup.agreement_support_level":"sketchup_agreement_support_level","autobooks_import_sketchup_data.agreement_days_due":"sketchup_agreement_days_due","sketchup.agreement_days_due":"sketchup_agreement_days_due","autobooks_import_sketchup_data.agreement_autorenew":"sketchup_agreement_autorenew","sketchup.agreement_autorenew":"sketchup_agreement_autorenew","autobooks_import_sketchup_data.product_name":"sketchup_product_name","sketchup.product_name":"sketchup_product_name","autobooks_import_sketchup_data.product_family":"product_name","sketchup.product_family":"product_name","autobooks_import_sketchup_data.product_market_segment":"sketchup_product_market_segment","sketchup.product_market_segment":"sketchup_product_market_segment","autobooks_import_sketchup_data.product_release":"sketchup_product_release","sketchup.product_release":"sketchup_product_release","autobooks_import_sketchup_data.product_type":"sketchup_product_type","sketchup.product_type":"sketchup_product_type","autobooks_import_sketchup_data.product_deployment":"sketchup_product_deployment","sketchup.product_deployment":"sketchup_product_deployment","autobooks_import_sketchup_data.product_sku":"sketchup_product_sku","sketchup.product_sku":"sketchup_product_sku","autobooks_import_sketchup_data.product_sku_description":"sketchup_product_sku_description","sketchup.product_sku_description":"sketchup_product_sku_description","autobooks_import_sketchup_data.product_part":"sketchup_product_part","sketchup.product_part":"sketchup_product_part","autobooks_import_sketchup_data.product_list_price":"price","sketchup.product_list_price":"price","autobooks_import_sketchup_data.product_list_price_currency":"currency","sketchup.product_list_price_currency":"currency","autobooks_import_sketchup_data.subscription_id":"sketchup_subscription_id","sketchup.subscription_id":"sketchup_subscription_id","autobooks_import_sketchup_data.subscription_serial_number":"serial_number","sketchup.subscription_serial_number":"serial_number","autobooks_import_sketchup_data.subscription_status":"sketchup_subscription_status","sketchup.subscription_status":"sketchup_subscription_status","autobooks_import_sketchup_data.subscription_quantity":"quantity","sketchup.subscription_quantity":"quantity","autobooks_import_sketchup_data.subscription_start_date":"sketchup_subscription_start_date","sketchup.subscription_start_date":"sketchup_subscription_start_date","autobooks_import_sketchup_data.subscription_end_date":"sketchup_subscription_end_date","sketchup.subscription_end_date":"sketchup_subscription_end_date","autobooks_import_sketchup_data.subscription_contact_name":"sketchup_subscription_contact_name","sketchup.subscription_contact_name":"sketchup_subscription_contact_name","autobooks_import_sketchup_data.subscription_contact_email":"sketchup_subscription_contact_email","sketchup.subscription_contact_email":"sketchup_subscription_contact_email","autobooks_import_sketchup_data.subscription_level":"sketchup_subscription_level","sketchup.subscription_level":"sketchup_subscription_level","autobooks_import_sketchup_data.subscription_days_due":"sketchup_subscription_days_due","sketchup.subscription_days_due":"sketchup_subscription_days_due","autobooks_import_sketchup_data.quotation_id":"sketchup_quotation_id","sketchup.quotation_id":"sketchup_quotation_id","autobooks_import_sketchup_data.quotation_type":"sketchup_quotation_type","sketchup.quotation_type":"sketchup_quotation_type","autobooks_import_sketchup_data.quotation_vendor_id":"sketchup_quotation_vendor_id","sketchup.quotation_vendor_id":"sketchup_quotation_vendor_id","autobooks_import_sketchup_data.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","sketchup.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","autobooks_import_sketchup_data.quotation_status":"sketchup_quotation_status","sketchup.quotation_status":"sketchup_quotation_status","autobooks_import_sketchup_data.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","sketchup.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","autobooks_import_sketchup_data.quotation_due_date":"sketchup_quotation_due_date","sketchup.quotation_due_date":"sketchup_quotation_due_date","autobooks_import_sketchup_data.flaer_phase":"sketchup_flaer_phase","sketchup.flaer_phase":"sketchup_flaer_phase","autobooks_import_sketchup_data.updated":"sketchup_updated","sketchup.updated":"sketchup_updated","autobooks_import_sketchup_data.created_at":"sketchup_created_at","sketchup.created_at":"sketchup_created_at","autobooks_import_sketchup_data.updated_at":"sketchup_updated_at","sketchup.updated_at":"sketchup_updated_at"}
[data_source_manager] [2025-08-20 10:01:14] [data_source_manager.class.php:267] Created data source: CSV Import: Sketchup with ID: 74
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.id => sketchup_id AND sketchup.id => sketchup_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.sold_to_name => company_name AND sketchup.sold_to_name => company_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.sold_to_number => sketchup_sold_to_number AND sketchup.sold_to_number => sketchup_sold_to_number
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: vendor_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for vendor_name: found 3 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback vendor_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1539] Fallback success: vendor_name -> contact_name (score: 56.5, confidence: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.vendor_name => contact_name AND sketchup.vendor_name => contact_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.reseller_number => reseller_name AND sketchup.reseller_number => reseller_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: reseller_vendor_id -> reseller_name (score: 54.5) loses to reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for reseller_vendor_id: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback reseller_vendor_id -> reseller_name: score 54.5 loses to existing reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for reseller_vendor_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.reseller_vendor_id => sketchup_reseller_vendor_id AND sketchup.reseller_vendor_id => sketchup_reseller_vendor_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_vendor_id => sketchup_end_customer_vendor_id AND sketchup.end_customer_vendor_id => sketchup_end_customer_vendor_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: end_customer_name -> company_name (score: 67.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for end_customer_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback end_customer_name -> company_name: score 65.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback end_customer_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_name => sketchup_end_customer_name AND sketchup.end_customer_name => sketchup_end_customer_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_1 => address AND sketchup.end_customer_address_1 => address
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_2 => sketchup_end_customer_address_2 AND sketchup.end_customer_address_2 => sketchup_end_customer_address_2
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_3 => sketchup_end_customer_address_3 AND sketchup.end_customer_address_3 => sketchup_end_customer_address_3
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_city => city AND sketchup.end_customer_city => city
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_state => state AND sketchup.end_customer_state => state
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_zip_code => postal_code AND sketchup.end_customer_zip_code => postal_code
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_country => country AND sketchup.end_customer_country => country
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_account_type => sketchup_end_customer_account_type AND sketchup.end_customer_account_type => sketchup_end_customer_account_type
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: end_customer_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for end_customer_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_contact_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_name => sketchup_end_customer_contact_name AND sketchup.end_customer_contact_name => sketchup_end_customer_contact_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_email => email AND sketchup.end_customer_contact_email => email
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_phone => sketchup_end_customer_contact_phone AND sketchup.end_customer_contact_phone => sketchup_end_customer_contact_phone
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_industry_segment => sketchup_end_customer_industry_segment AND sketchup.end_customer_industry_segment => sketchup_end_customer_industry_segment
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_program_name => product_name AND sketchup.agreement_program_name => product_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_number => subscription_reference AND sketchup.agreement_number => subscription_reference
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_start_date => start_date AND sketchup.agreement_start_date => start_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_end_date => end_date AND sketchup.agreement_end_date => end_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_terms => sketchup_agreement_terms AND sketchup.agreement_terms => sketchup_agreement_terms
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_type => sketchup_agreement_type AND sketchup.agreement_type => sketchup_agreement_type
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_status => status AND sketchup.agreement_status => status
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_support_level => sketchup_agreement_support_level AND sketchup.agreement_support_level => sketchup_agreement_support_level
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_days_due => sketchup_agreement_days_due AND sketchup.agreement_days_due => sketchup_agreement_days_due
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_autorenew => sketchup_agreement_autorenew AND sketchup.agreement_autorenew => sketchup_agreement_autorenew
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_name -> product_name (score: 70) loses to agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_name: found 3 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_name -> product_name: score 68 loses to existing agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_name => sketchup_product_name AND sketchup.product_name => sketchup_product_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:325] Alias conflict: product_family -> product_name (score: 79) replaces agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_family => product_name AND sketchup.product_family => product_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_market_segment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_market_segment: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_market_segment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_market_segment
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_market_segment => sketchup_product_market_segment AND sketchup.product_market_segment => sketchup_product_market_segment
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_release -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_release: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_release -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_release
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_release => sketchup_product_release AND sketchup.product_release => sketchup_product_release
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_type -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_type: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_type -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_type
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_type => sketchup_product_type AND sketchup.product_type => sketchup_product_type
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_deployment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_deployment: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_deployment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_deployment
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_deployment => sketchup_product_deployment AND sketchup.product_deployment => sketchup_product_deployment
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_sku -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_sku: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_sku -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_sku
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku => sketchup_product_sku AND sketchup.product_sku => sketchup_product_sku
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_sku_description -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_sku_description: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_sku_description -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_sku_description
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku_description => sketchup_product_sku_description AND sketchup.product_sku_description => sketchup_product_sku_description
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_part -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_part: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_part -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for product_part
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_part => sketchup_product_part AND sketchup.product_part => sketchup_product_part
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_list_price -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_list_price: found 2 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_list_price -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1539] Fallback success: product_list_price -> price (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price => price AND sketchup.product_list_price => price
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: product_list_price_currency -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for product_list_price_currency: found 3 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback product_list_price_currency -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1539] Fallback success: product_list_price_currency -> currency (score: 61, confidence: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price_currency => currency AND sketchup.product_list_price_currency => currency
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_id -> subscription_reference (score: 85) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_id: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_id -> subscription_reference: score 83 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for subscription_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_id => sketchup_subscription_id AND sketchup.subscription_id => sketchup_subscription_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_serial_number -> subscription_reference (score: 79) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_serial_number: found 2 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_serial_number -> subscription_reference: score 79 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1539] Fallback success: subscription_serial_number -> serial_number (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_serial_number => serial_number AND sketchup.subscription_serial_number => serial_number
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_status -> status (score: 58) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_status: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_status -> status: score 56 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for subscription_status
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_status => sketchup_subscription_status AND sketchup.subscription_status => sketchup_subscription_status
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_quantity => quantity AND sketchup.subscription_quantity => quantity
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_start_date -> start_date (score: 58) loses to agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_start_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_start_date -> start_date: score 56 loses to existing agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for subscription_start_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_start_date => sketchup_subscription_start_date AND sketchup.subscription_start_date => sketchup_subscription_start_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_end_date -> end_date (score: 58) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_end_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_end_date -> end_date: score 56 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for subscription_end_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_end_date => sketchup_subscription_end_date AND sketchup.subscription_end_date => sketchup_subscription_end_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_name => sketchup_subscription_contact_name AND sketchup.subscription_contact_name => sketchup_subscription_contact_name
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_email: found 2 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback subscription_contact_email -> email: score 82 loses to existing end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1521] Fallback subscription_contact_email -> contact_name: confidence 56 below threshold 60
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_email
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_email => sketchup_subscription_contact_email AND sketchup.subscription_contact_email => sketchup_subscription_contact_email
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_level => sketchup_subscription_level AND sketchup.subscription_level => sketchup_subscription_level
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_days_due => sketchup_subscription_days_due AND sketchup.subscription_days_due => sketchup_subscription_days_due
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_id => sketchup_quotation_id AND sketchup.quotation_id => sketchup_quotation_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_type => sketchup_quotation_type AND sketchup.quotation_type => sketchup_quotation_type
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_vendor_id => sketchup_quotation_vendor_id AND sketchup.quotation_vendor_id => sketchup_quotation_vendor_id
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_deal_registration_number => sketchup_quotation_deal_registration_number AND sketchup.quotation_deal_registration_number => sketchup_quotation_deal_registration_number
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: quotation_status -> status (score: 52) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for quotation_status: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback quotation_status -> status: score 52 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for quotation_status
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_status => sketchup_quotation_status AND sketchup.quotation_status => sketchup_quotation_status
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous AND sketchup.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:327] Alias conflict: quotation_due_date -> end_date (score: 52) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1511] Trying fallback for quotation_due_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1534] Fallback quotation_due_date -> end_date: score 52 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:1544] No suitable fallback found for quotation_due_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_due_date => sketchup_quotation_due_date AND sketchup.quotation_due_date => sketchup_quotation_due_date
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.flaer_phase => sketchup_flaer_phase AND sketchup.flaer_phase => sketchup_flaer_phase
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated => sketchup_updated AND sketchup.updated => sketchup_updated
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.created_at => sketchup_created_at AND sketchup.created_at => sketchup_created_at
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated_at => sketchup_updated_at AND sketchup.updated_at => sketchup_updated_at
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:187] Generated 124 column aliases: {"autobooks_import_sketchup_data.id":"sketchup_id","sketchup.id":"sketchup_id","autobooks_import_sketchup_data.sold_to_name":"company_name","sketchup.sold_to_name":"company_name","autobooks_import_sketchup_data.sold_to_number":"sketchup_sold_to_number","sketchup.sold_to_number":"sketchup_sold_to_number","autobooks_import_sketchup_data.vendor_name":"contact_name","sketchup.vendor_name":"contact_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","sketchup.reseller_number":"reseller_name","autobooks_import_sketchup_data.reseller_vendor_id":"sketchup_reseller_vendor_id","sketchup.reseller_vendor_id":"sketchup_reseller_vendor_id","autobooks_import_sketchup_data.end_customer_vendor_id":"sketchup_end_customer_vendor_id","sketchup.end_customer_vendor_id":"sketchup_end_customer_vendor_id","autobooks_import_sketchup_data.end_customer_name":"sketchup_end_customer_name","sketchup.end_customer_name":"sketchup_end_customer_name","autobooks_import_sketchup_data.end_customer_address_1":"address","sketchup.end_customer_address_1":"address","autobooks_import_sketchup_data.end_customer_address_2":"sketchup_end_customer_address_2","sketchup.end_customer_address_2":"sketchup_end_customer_address_2","autobooks_import_sketchup_data.end_customer_address_3":"sketchup_end_customer_address_3","sketchup.end_customer_address_3":"sketchup_end_customer_address_3","autobooks_import_sketchup_data.end_customer_city":"city","sketchup.end_customer_city":"city","autobooks_import_sketchup_data.end_customer_state":"state","sketchup.end_customer_state":"state","autobooks_import_sketchup_data.end_customer_zip_code":"postal_code","sketchup.end_customer_zip_code":"postal_code","autobooks_import_sketchup_data.end_customer_country":"country","sketchup.end_customer_country":"country","autobooks_import_sketchup_data.end_customer_account_type":"sketchup_end_customer_account_type","sketchup.end_customer_account_type":"sketchup_end_customer_account_type","autobooks_import_sketchup_data.end_customer_contact_name":"sketchup_end_customer_contact_name","sketchup.end_customer_contact_name":"sketchup_end_customer_contact_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","sketchup.end_customer_contact_email":"email","autobooks_import_sketchup_data.end_customer_contact_phone":"sketchup_end_customer_contact_phone","sketchup.end_customer_contact_phone":"sketchup_end_customer_contact_phone","autobooks_import_sketchup_data.end_customer_industry_segment":"sketchup_end_customer_industry_segment","sketchup.end_customer_industry_segment":"sketchup_end_customer_industry_segment","autobooks_import_sketchup_data.agreement_program_name":"product_name","sketchup.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","sketchup.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"start_date","sketchup.agreement_start_date":"start_date","autobooks_import_sketchup_data.agreement_end_date":"end_date","sketchup.agreement_end_date":"end_date","autobooks_import_sketchup_data.agreement_terms":"sketchup_agreement_terms","sketchup.agreement_terms":"sketchup_agreement_terms","autobooks_import_sketchup_data.agreement_type":"sketchup_agreement_type","sketchup.agreement_type":"sketchup_agreement_type","autobooks_import_sketchup_data.agreement_status":"status","sketchup.agreement_status":"status","autobooks_import_sketchup_data.agreement_support_level":"sketchup_agreement_support_level","sketchup.agreement_support_level":"sketchup_agreement_support_level","autobooks_import_sketchup_data.agreement_days_due":"sketchup_agreement_days_due","sketchup.agreement_days_due":"sketchup_agreement_days_due","autobooks_import_sketchup_data.agreement_autorenew":"sketchup_agreement_autorenew","sketchup.agreement_autorenew":"sketchup_agreement_autorenew","autobooks_import_sketchup_data.product_name":"sketchup_product_name","sketchup.product_name":"sketchup_product_name","autobooks_import_sketchup_data.product_family":"product_name","sketchup.product_family":"product_name","autobooks_import_sketchup_data.product_market_segment":"sketchup_product_market_segment","sketchup.product_market_segment":"sketchup_product_market_segment","autobooks_import_sketchup_data.product_release":"sketchup_product_release","sketchup.product_release":"sketchup_product_release","autobooks_import_sketchup_data.product_type":"sketchup_product_type","sketchup.product_type":"sketchup_product_type","autobooks_import_sketchup_data.product_deployment":"sketchup_product_deployment","sketchup.product_deployment":"sketchup_product_deployment","autobooks_import_sketchup_data.product_sku":"sketchup_product_sku","sketchup.product_sku":"sketchup_product_sku","autobooks_import_sketchup_data.product_sku_description":"sketchup_product_sku_description","sketchup.product_sku_description":"sketchup_product_sku_description","autobooks_import_sketchup_data.product_part":"sketchup_product_part","sketchup.product_part":"sketchup_product_part","autobooks_import_sketchup_data.product_list_price":"price","sketchup.product_list_price":"price","autobooks_import_sketchup_data.product_list_price_currency":"currency","sketchup.product_list_price_currency":"currency","autobooks_import_sketchup_data.subscription_id":"sketchup_subscription_id","sketchup.subscription_id":"sketchup_subscription_id","autobooks_import_sketchup_data.subscription_serial_number":"serial_number","sketchup.subscription_serial_number":"serial_number","autobooks_import_sketchup_data.subscription_status":"sketchup_subscription_status","sketchup.subscription_status":"sketchup_subscription_status","autobooks_import_sketchup_data.subscription_quantity":"quantity","sketchup.subscription_quantity":"quantity","autobooks_import_sketchup_data.subscription_start_date":"sketchup_subscription_start_date","sketchup.subscription_start_date":"sketchup_subscription_start_date","autobooks_import_sketchup_data.subscription_end_date":"sketchup_subscription_end_date","sketchup.subscription_end_date":"sketchup_subscription_end_date","autobooks_import_sketchup_data.subscription_contact_name":"sketchup_subscription_contact_name","sketchup.subscription_contact_name":"sketchup_subscription_contact_name","autobooks_import_sketchup_data.subscription_contact_email":"sketchup_subscription_contact_email","sketchup.subscription_contact_email":"sketchup_subscription_contact_email","autobooks_import_sketchup_data.subscription_level":"sketchup_subscription_level","sketchup.subscription_level":"sketchup_subscription_level","autobooks_import_sketchup_data.subscription_days_due":"sketchup_subscription_days_due","sketchup.subscription_days_due":"sketchup_subscription_days_due","autobooks_import_sketchup_data.quotation_id":"sketchup_quotation_id","sketchup.quotation_id":"sketchup_quotation_id","autobooks_import_sketchup_data.quotation_type":"sketchup_quotation_type","sketchup.quotation_type":"sketchup_quotation_type","autobooks_import_sketchup_data.quotation_vendor_id":"sketchup_quotation_vendor_id","sketchup.quotation_vendor_id":"sketchup_quotation_vendor_id","autobooks_import_sketchup_data.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","sketchup.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","autobooks_import_sketchup_data.quotation_status":"sketchup_quotation_status","sketchup.quotation_status":"sketchup_quotation_status","autobooks_import_sketchup_data.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","sketchup.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","autobooks_import_sketchup_data.quotation_due_date":"sketchup_quotation_due_date","sketchup.quotation_due_date":"sketchup_quotation_due_date","autobooks_import_sketchup_data.flaer_phase":"sketchup_flaer_phase","sketchup.flaer_phase":"sketchup_flaer_phase","autobooks_import_sketchup_data.updated":"sketchup_updated","sketchup.updated":"sketchup_updated","autobooks_import_sketchup_data.created_at":"sketchup_created_at","sketchup.created_at":"sketchup_created_at","autobooks_import_sketchup_data.updated_at":"sketchup_updated_at","sketchup.updated_at":"sketchup_updated_at"}
[data_source_manager] [2025-08-20 10:03:33] [data_source_manager.class.php:267] Created data source: CSV Import: Sketchup with ID: 75
[data_source_manager] [2025-08-20 10:53:11] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 10:53:11] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-08-20 10:53:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 10:53:15] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:165] Auto-generating column aliases for unified field mapping
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.id => sketchup_id AND sketchup.id => sketchup_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.sold_to_name => company_name AND sketchup.sold_to_name => company_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.sold_to_number => sketchup_sold_to_number AND sketchup.sold_to_number => sketchup_sold_to_number
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: vendor_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for vendor_name: found 3 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback vendor_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1539] Fallback success: vendor_name -> contact_name (score: 56.5, confidence: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.vendor_name => contact_name AND sketchup.vendor_name => contact_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.reseller_number => reseller_name AND sketchup.reseller_number => reseller_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: reseller_vendor_id -> reseller_name (score: 54.5) loses to reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for reseller_vendor_id: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback reseller_vendor_id -> reseller_name: score 54.5 loses to existing reseller_number (score: 54.5)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for reseller_vendor_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.reseller_vendor_id => sketchup_reseller_vendor_id AND sketchup.reseller_vendor_id => sketchup_reseller_vendor_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_vendor_id => sketchup_end_customer_vendor_id AND sketchup.end_customer_vendor_id => sketchup_end_customer_vendor_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: end_customer_name -> company_name (score: 67.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for end_customer_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback end_customer_name -> company_name: score 65.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback end_customer_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_name => sketchup_end_customer_name AND sketchup.end_customer_name => sketchup_end_customer_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_address_1 => address AND sketchup.end_customer_address_1 => address
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_2 => sketchup_end_customer_address_2 AND sketchup.end_customer_address_2 => sketchup_end_customer_address_2
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_address_3 => sketchup_end_customer_address_3 AND sketchup.end_customer_address_3 => sketchup_end_customer_address_3
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_city => city AND sketchup.end_customer_city => city
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_state => state AND sketchup.end_customer_state => state
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_zip_code => postal_code AND sketchup.end_customer_zip_code => postal_code
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_country => country AND sketchup.end_customer_country => country
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_account_type => sketchup_end_customer_account_type AND sketchup.end_customer_account_type => sketchup_end_customer_account_type
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: end_customer_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for end_customer_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback end_customer_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for end_customer_contact_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_name => sketchup_end_customer_contact_name AND sketchup.end_customer_contact_name => sketchup_end_customer_contact_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.end_customer_contact_email => email AND sketchup.end_customer_contact_email => email
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_contact_phone => sketchup_end_customer_contact_phone AND sketchup.end_customer_contact_phone => sketchup_end_customer_contact_phone
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.end_customer_industry_segment => sketchup_end_customer_industry_segment AND sketchup.end_customer_industry_segment => sketchup_end_customer_industry_segment
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_program_name => product_name AND sketchup.agreement_program_name => product_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_number => subscription_reference AND sketchup.agreement_number => subscription_reference
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_start_date => start_date AND sketchup.agreement_start_date => start_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_end_date => end_date AND sketchup.agreement_end_date => end_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_terms => sketchup_agreement_terms AND sketchup.agreement_terms => sketchup_agreement_terms
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_type => sketchup_agreement_type AND sketchup.agreement_type => sketchup_agreement_type
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.agreement_status => status AND sketchup.agreement_status => status
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_support_level => sketchup_agreement_support_level AND sketchup.agreement_support_level => sketchup_agreement_support_level
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_days_due => sketchup_agreement_days_due AND sketchup.agreement_days_due => sketchup_agreement_days_due
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.agreement_autorenew => sketchup_agreement_autorenew AND sketchup.agreement_autorenew => sketchup_agreement_autorenew
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_name -> product_name (score: 70) loses to agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_name: found 3 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_name -> product_name: score 68 loses to existing agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_name -> contact_name: score 56.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_name => sketchup_product_name AND sketchup.product_name => sketchup_product_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:325] Alias conflict: product_family -> product_name (score: 79) replaces agreement_program_name (score: 70)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_family => product_name AND sketchup.product_family => product_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_market_segment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_market_segment: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_market_segment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_market_segment
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_market_segment => sketchup_product_market_segment AND sketchup.product_market_segment => sketchup_product_market_segment
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_release -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_release: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_release -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_release
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_release => sketchup_product_release AND sketchup.product_release => sketchup_product_release
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_type -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_type: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_type -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_type
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_type => sketchup_product_type AND sketchup.product_type => sketchup_product_type
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_deployment -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_deployment: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_deployment -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_deployment
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_deployment => sketchup_product_deployment AND sketchup.product_deployment => sketchup_product_deployment
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_sku -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_sku: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_sku -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_sku
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku => sketchup_product_sku AND sketchup.product_sku => sketchup_product_sku
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_sku_description -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_sku_description: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_sku_description -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_sku_description
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_sku_description => sketchup_product_sku_description AND sketchup.product_sku_description => sketchup_product_sku_description
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_part -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_part: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_part -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for product_part
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.product_part => sketchup_product_part AND sketchup.product_part => sketchup_product_part
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_list_price -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_list_price: found 2 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_list_price -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1539] Fallback success: product_list_price -> price (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price => price AND sketchup.product_list_price => price
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: product_list_price_currency -> product_name (score: 62) loses to product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for product_list_price_currency: found 3 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback product_list_price_currency -> product_name: score 62 loses to existing product_family (score: 79)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1539] Fallback success: product_list_price_currency -> currency (score: 61, confidence: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.product_list_price_currency => currency AND sketchup.product_list_price_currency => currency
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_id -> subscription_reference (score: 85) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_id: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_id -> subscription_reference: score 83 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_id => sketchup_subscription_id AND sketchup.subscription_id => sketchup_subscription_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_serial_number -> subscription_reference (score: 79) loses to agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_serial_number: found 2 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_serial_number -> subscription_reference: score 79 loses to existing agreement_number (score: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1539] Fallback success: subscription_serial_number -> serial_number (score: 52, confidence: 85)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_serial_number => serial_number AND sketchup.subscription_serial_number => serial_number
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_status -> status (score: 58) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_status: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_status -> status: score 56 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_status
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_status => sketchup_subscription_status AND sketchup.subscription_status => sketchup_subscription_status
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:359] Generated aliases: autobooks_import_sketchup_data.subscription_quantity => quantity AND sketchup.subscription_quantity => quantity
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_start_date -> start_date (score: 58) loses to agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_start_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_start_date -> start_date: score 56 loses to existing agreement_start_date (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_start_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_start_date => sketchup_subscription_start_date AND sketchup.subscription_start_date => sketchup_subscription_start_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_end_date -> end_date (score: 58) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_end_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_end_date -> end_date: score 56 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_end_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_end_date => sketchup_subscription_end_date AND sketchup.subscription_end_date => sketchup_subscription_end_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_contact_name -> company_name (score: 63.4) loses to sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_name: found 2 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> company_name: score 63.4 loses to existing sold_to_name (score: 67.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_contact_name -> contact_name: score 60.5 loses to existing vendor_name (score: 63.4)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_name => sketchup_subscription_contact_name AND sketchup.subscription_contact_name => sketchup_subscription_contact_name
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: subscription_contact_email -> email (score: 82) loses to end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for subscription_contact_email: found 2 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback subscription_contact_email -> email: score 82 loses to existing end_customer_contact_email (score: 82)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1521] Fallback subscription_contact_email -> contact_name: confidence 56 below threshold 60
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for subscription_contact_email
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_contact_email => sketchup_subscription_contact_email AND sketchup.subscription_contact_email => sketchup_subscription_contact_email
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_level => sketchup_subscription_level AND sketchup.subscription_level => sketchup_subscription_level
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.subscription_days_due => sketchup_subscription_days_due AND sketchup.subscription_days_due => sketchup_subscription_days_due
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_id => sketchup_quotation_id AND sketchup.quotation_id => sketchup_quotation_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_type => sketchup_quotation_type AND sketchup.quotation_type => sketchup_quotation_type
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_vendor_id => sketchup_quotation_vendor_id AND sketchup.quotation_vendor_id => sketchup_quotation_vendor_id
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_deal_registration_number => sketchup_quotation_deal_registration_number AND sketchup.quotation_deal_registration_number => sketchup_quotation_deal_registration_number
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: quotation_status -> status (score: 52) loses to agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for quotation_status: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback quotation_status -> status: score 52 loses to existing agreement_status (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for quotation_status
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_status => sketchup_quotation_status AND sketchup.quotation_status => sketchup_quotation_status
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous AND sketchup.quotation_resellerpo_previous => sketchup_quotation_resellerpo_previous
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:327] Alias conflict: quotation_due_date -> end_date (score: 52) loses to agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1511] Trying fallback for quotation_due_date: found 1 alternatives
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1534] Fallback quotation_due_date -> end_date: score 52 loses to existing agreement_end_date (score: 58)
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:1544] No suitable fallback found for quotation_due_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.quotation_due_date => sketchup_quotation_due_date AND sketchup.quotation_due_date => sketchup_quotation_due_date
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.flaer_phase => sketchup_flaer_phase AND sketchup.flaer_phase => sketchup_flaer_phase
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated => sketchup_updated AND sketchup.updated => sketchup_updated
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.created_at => sketchup_created_at AND sketchup.created_at => sketchup_created_at
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:377] Generated fallback aliases: autobooks_import_sketchup_data.updated_at => sketchup_updated_at AND sketchup.updated_at => sketchup_updated_at
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:187] Generated 124 column aliases: {"autobooks_import_sketchup_data.id":"sketchup_id","sketchup.id":"sketchup_id","autobooks_import_sketchup_data.sold_to_name":"company_name","sketchup.sold_to_name":"company_name","autobooks_import_sketchup_data.sold_to_number":"sketchup_sold_to_number","sketchup.sold_to_number":"sketchup_sold_to_number","autobooks_import_sketchup_data.vendor_name":"contact_name","sketchup.vendor_name":"contact_name","autobooks_import_sketchup_data.reseller_number":"reseller_name","sketchup.reseller_number":"reseller_name","autobooks_import_sketchup_data.reseller_vendor_id":"sketchup_reseller_vendor_id","sketchup.reseller_vendor_id":"sketchup_reseller_vendor_id","autobooks_import_sketchup_data.end_customer_vendor_id":"sketchup_end_customer_vendor_id","sketchup.end_customer_vendor_id":"sketchup_end_customer_vendor_id","autobooks_import_sketchup_data.end_customer_name":"sketchup_end_customer_name","sketchup.end_customer_name":"sketchup_end_customer_name","autobooks_import_sketchup_data.end_customer_address_1":"address","sketchup.end_customer_address_1":"address","autobooks_import_sketchup_data.end_customer_address_2":"sketchup_end_customer_address_2","sketchup.end_customer_address_2":"sketchup_end_customer_address_2","autobooks_import_sketchup_data.end_customer_address_3":"sketchup_end_customer_address_3","sketchup.end_customer_address_3":"sketchup_end_customer_address_3","autobooks_import_sketchup_data.end_customer_city":"city","sketchup.end_customer_city":"city","autobooks_import_sketchup_data.end_customer_state":"state","sketchup.end_customer_state":"state","autobooks_import_sketchup_data.end_customer_zip_code":"postal_code","sketchup.end_customer_zip_code":"postal_code","autobooks_import_sketchup_data.end_customer_country":"country","sketchup.end_customer_country":"country","autobooks_import_sketchup_data.end_customer_account_type":"sketchup_end_customer_account_type","sketchup.end_customer_account_type":"sketchup_end_customer_account_type","autobooks_import_sketchup_data.end_customer_contact_name":"sketchup_end_customer_contact_name","sketchup.end_customer_contact_name":"sketchup_end_customer_contact_name","autobooks_import_sketchup_data.end_customer_contact_email":"email","sketchup.end_customer_contact_email":"email","autobooks_import_sketchup_data.end_customer_contact_phone":"sketchup_end_customer_contact_phone","sketchup.end_customer_contact_phone":"sketchup_end_customer_contact_phone","autobooks_import_sketchup_data.end_customer_industry_segment":"sketchup_end_customer_industry_segment","sketchup.end_customer_industry_segment":"sketchup_end_customer_industry_segment","autobooks_import_sketchup_data.agreement_program_name":"product_name","sketchup.agreement_program_name":"product_name","autobooks_import_sketchup_data.agreement_number":"subscription_reference","sketchup.agreement_number":"subscription_reference","autobooks_import_sketchup_data.agreement_start_date":"start_date","sketchup.agreement_start_date":"start_date","autobooks_import_sketchup_data.agreement_end_date":"end_date","sketchup.agreement_end_date":"end_date","autobooks_import_sketchup_data.agreement_terms":"sketchup_agreement_terms","sketchup.agreement_terms":"sketchup_agreement_terms","autobooks_import_sketchup_data.agreement_type":"sketchup_agreement_type","sketchup.agreement_type":"sketchup_agreement_type","autobooks_import_sketchup_data.agreement_status":"status","sketchup.agreement_status":"status","autobooks_import_sketchup_data.agreement_support_level":"sketchup_agreement_support_level","sketchup.agreement_support_level":"sketchup_agreement_support_level","autobooks_import_sketchup_data.agreement_days_due":"sketchup_agreement_days_due","sketchup.agreement_days_due":"sketchup_agreement_days_due","autobooks_import_sketchup_data.agreement_autorenew":"sketchup_agreement_autorenew","sketchup.agreement_autorenew":"sketchup_agreement_autorenew","autobooks_import_sketchup_data.product_name":"sketchup_product_name","sketchup.product_name":"sketchup_product_name","autobooks_import_sketchup_data.product_family":"product_name","sketchup.product_family":"product_name","autobooks_import_sketchup_data.product_market_segment":"sketchup_product_market_segment","sketchup.product_market_segment":"sketchup_product_market_segment","autobooks_import_sketchup_data.product_release":"sketchup_product_release","sketchup.product_release":"sketchup_product_release","autobooks_import_sketchup_data.product_type":"sketchup_product_type","sketchup.product_type":"sketchup_product_type","autobooks_import_sketchup_data.product_deployment":"sketchup_product_deployment","sketchup.product_deployment":"sketchup_product_deployment","autobooks_import_sketchup_data.product_sku":"sketchup_product_sku","sketchup.product_sku":"sketchup_product_sku","autobooks_import_sketchup_data.product_sku_description":"sketchup_product_sku_description","sketchup.product_sku_description":"sketchup_product_sku_description","autobooks_import_sketchup_data.product_part":"sketchup_product_part","sketchup.product_part":"sketchup_product_part","autobooks_import_sketchup_data.product_list_price":"price","sketchup.product_list_price":"price","autobooks_import_sketchup_data.product_list_price_currency":"currency","sketchup.product_list_price_currency":"currency","autobooks_import_sketchup_data.subscription_id":"sketchup_subscription_id","sketchup.subscription_id":"sketchup_subscription_id","autobooks_import_sketchup_data.subscription_serial_number":"serial_number","sketchup.subscription_serial_number":"serial_number","autobooks_import_sketchup_data.subscription_status":"sketchup_subscription_status","sketchup.subscription_status":"sketchup_subscription_status","autobooks_import_sketchup_data.subscription_quantity":"quantity","sketchup.subscription_quantity":"quantity","autobooks_import_sketchup_data.subscription_start_date":"sketchup_subscription_start_date","sketchup.subscription_start_date":"sketchup_subscription_start_date","autobooks_import_sketchup_data.subscription_end_date":"sketchup_subscription_end_date","sketchup.subscription_end_date":"sketchup_subscription_end_date","autobooks_import_sketchup_data.subscription_contact_name":"sketchup_subscription_contact_name","sketchup.subscription_contact_name":"sketchup_subscription_contact_name","autobooks_import_sketchup_data.subscription_contact_email":"sketchup_subscription_contact_email","sketchup.subscription_contact_email":"sketchup_subscription_contact_email","autobooks_import_sketchup_data.subscription_level":"sketchup_subscription_level","sketchup.subscription_level":"sketchup_subscription_level","autobooks_import_sketchup_data.subscription_days_due":"sketchup_subscription_days_due","sketchup.subscription_days_due":"sketchup_subscription_days_due","autobooks_import_sketchup_data.quotation_id":"sketchup_quotation_id","sketchup.quotation_id":"sketchup_quotation_id","autobooks_import_sketchup_data.quotation_type":"sketchup_quotation_type","sketchup.quotation_type":"sketchup_quotation_type","autobooks_import_sketchup_data.quotation_vendor_id":"sketchup_quotation_vendor_id","sketchup.quotation_vendor_id":"sketchup_quotation_vendor_id","autobooks_import_sketchup_data.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","sketchup.quotation_deal_registration_number":"sketchup_quotation_deal_registration_number","autobooks_import_sketchup_data.quotation_status":"sketchup_quotation_status","sketchup.quotation_status":"sketchup_quotation_status","autobooks_import_sketchup_data.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","sketchup.quotation_resellerpo_previous":"sketchup_quotation_resellerpo_previous","autobooks_import_sketchup_data.quotation_due_date":"sketchup_quotation_due_date","sketchup.quotation_due_date":"sketchup_quotation_due_date","autobooks_import_sketchup_data.flaer_phase":"sketchup_flaer_phase","sketchup.flaer_phase":"sketchup_flaer_phase","autobooks_import_sketchup_data.updated":"sketchup_updated","sketchup.updated":"sketchup_updated","autobooks_import_sketchup_data.created_at":"sketchup_created_at","sketchup.created_at":"sketchup_created_at","autobooks_import_sketchup_data.updated_at":"sketchup_updated_at","sketchup.updated_at":"sketchup_updated_at"}
[data_source_manager] [2025-08-20 10:53:49] [data_source_manager.class.php:267] Created data source: CSV Import: Sketchup with ID: 76
[data_source_manager] [2025-08-20 10:53:53] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 10:53:53] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-08-20 10:53:55] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 10:53:55] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
[data_source_manager] [2025-08-20 10:57:28] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup` LIMIT 50
[data_source_manager] [2025-08-20 10:57:28] [data_source_manager.class.php:640] Executing query: SELECT `sketchup`.`sold_to_name` AS `company_name`, `sketchup`.`sold_to_number` AS `sketchup_sold_to_number`, `sketchup`.`vendor_name` AS `contact_name`, `sketchup`.`reseller_number` AS `reseller_name`, `sketchup`.`reseller_vendor_id` AS `sketchup_reseller_vendor_id`, `sketchup`.`end_customer_vendor_id` AS `sketchup_end_customer_vendor_id`, `sketchup`.`end_customer_name` AS `sketchup_end_customer_name`, `sketchup`.`end_customer_address_1` AS `address`, `sketchup`.`end_customer_address_2` AS `sketchup_end_customer_address_2`, `sketchup`.`end_customer_address_3` AS `sketchup_end_customer_address_3`, `sketchup`.`end_customer_city` AS `city`, `sketchup`.`end_customer_state` AS `state`, `sketchup`.`end_customer_zip_code` AS `postal_code`, `sketchup`.`end_customer_country` AS `country`, `sketchup`.`end_customer_account_type` AS `sketchup_end_customer_account_type`, `sketchup`.`end_customer_contact_name` AS `sketchup_end_customer_contact_name`, `sketchup`.`end_customer_contact_email` AS `email`, `sketchup`.`end_customer_contact_phone` AS `sketchup_end_customer_contact_phone`, `sketchup`.`end_customer_industry_segment` AS `sketchup_end_customer_industry_segment`, `sketchup`.`agreement_program_name` AS `product_name`, `sketchup`.`agreement_number` AS `subscription_reference`, `sketchup`.`agreement_start_date` AS `start_date`, `sketchup`.`agreement_end_date` AS `end_date`, `sketchup`.`agreement_terms` AS `sketchup_agreement_terms`, `sketchup`.`agreement_type` AS `sketchup_agreement_type`, `sketchup`.`agreement_status` AS `status`, `sketchup`.`agreement_support_level` AS `sketchup_agreement_support_level`, `sketchup`.`agreement_days_due` AS `sketchup_agreement_days_due`, `sketchup`.`agreement_autorenew` AS `sketchup_agreement_autorenew`, `sketchup`.`product_name` AS `sketchup_product_name`, `sketchup`.`product_family` AS `product_name`, `sketchup`.`product_market_segment` AS `sketchup_product_market_segment`, `sketchup`.`product_release` AS `sketchup_product_release`, `sketchup`.`product_type` AS `sketchup_product_type`, `sketchup`.`product_deployment` AS `sketchup_product_deployment`, `sketchup`.`product_sku` AS `sketchup_product_sku`, `sketchup`.`product_sku_description` AS `sketchup_product_sku_description`, `sketchup`.`product_part` AS `sketchup_product_part`, `sketchup`.`product_list_price` AS `price`, `sketchup`.`product_list_price_currency` AS `currency`, `sketchup`.`subscription_id` AS `sketchup_subscription_id`, `sketchup`.`subscription_serial_number` AS `serial_number`, `sketchup`.`subscription_status` AS `sketchup_subscription_status`, `sketchup`.`subscription_quantity` AS `quantity`, `sketchup`.`subscription_start_date` AS `sketchup_subscription_start_date`, `sketchup`.`subscription_end_date` AS `sketchup_subscription_end_date`, `sketchup`.`subscription_contact_name` AS `sketchup_subscription_contact_name`, `sketchup`.`subscription_contact_email` AS `sketchup_subscription_contact_email`, `sketchup`.`subscription_level` AS `sketchup_subscription_level`, `sketchup`.`subscription_days_due` AS `sketchup_subscription_days_due`, `sketchup`.`quotation_id` AS `sketchup_quotation_id`, `sketchup`.`quotation_type` AS `sketchup_quotation_type`, `sketchup`.`quotation_vendor_id` AS `sketchup_quotation_vendor_id`, `sketchup`.`quotation_deal_registration_number` AS `sketchup_quotation_deal_registration_number`, `sketchup`.`quotation_status` AS `sketchup_quotation_status`, `sketchup`.`quotation_resellerpo_previous` AS `sketchup_quotation_resellerpo_previous`, `sketchup`.`quotation_due_date` AS `sketchup_quotation_due_date`, `sketchup`.`flaer_phase` AS `sketchup_flaer_phase`, `sketchup`.`updated` AS `sketchup_updated` FROM `autobooks_import_sketchup_data` AS `sketchup`
