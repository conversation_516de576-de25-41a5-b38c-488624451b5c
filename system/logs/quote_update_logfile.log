[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-20 09:45:08
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995219\n            [transactionId] => 3b431047-3f8b-5095-93f1-71549975113b\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-995219 status changed to Ordered.\n            [modifiedAt] => 2025-08-20T09:45:05.184Z\n        )\n\n    [publishedAt] => 2025-08-20T09:45:05.000Z\n    [csn] => 5103159758\n)\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-20 09:45:08
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7fcfd4b0-afe8-4afd-ba00-7adc31a18f6c\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995219\n            [transactionId] => 3b431047-3f8b-5095-93f1-71549975113b\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-995219 status changed to Ordered.\n            [modifiedAt] => 2025-08-20T09:45:05.184Z\n        )\n\n    [publishedAt] => 2025-08-20T09:45:05.000Z\n    [csn] => 5103159758\n)\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-20 09:45:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
