-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 20, 2025 at 02:23 PM
-- Table: customers_basket_attributes
-- Records: 94

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `customers_basket_attributes`
--

DROP TABLE IF EXISTS `customers_basket_attributes`;
CREATE TABLE `customers_basket_attributes` (
  `customers_basket_attributes_id` int(11) NOT NULL AUTO_INCREMENT,
  `customers_id` int(11) NOT NULL DEFAULT 0,
  `products_id` text COLLATE utf8_unicode_ci NOT NULL,
  `products_options_id` int(11) NOT NULL DEFAULT 0,
  `products_options_value_id` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`customers_basket_attributes_id`),
  KEY `idx_customers_basket_att_customers_id` (`customers_id`)
) ENGINE=MyISAM AUTO_INCREMENT=965 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Dumping data for table `customers_basket_attributes`
--

INSERT INTO `customers_basket_attributes` (`customers_basket_attributes_id`, `customers_id`, `products_id`, `products_options_id`, `products_options_value_id`) VALUES
('556', '14994', '10852{11}104', '11', '104'),
('45', '21068', '10851{11}104', '11', '104'),
('14', '21183', '10174{5}41{6}39', '5', '41'),
('15', '21183', '10174{5}41{6}39', '6', '39'),
('94', '1482', '11187{9}114{6}39', '9', '114'),
('325', '19093', '10066{11}105', '11', '105'),
('320', '21504', '10782{1}24', '1', '24'),
('134', '21403', '10851{11}104', '11', '104'),
('95', '1482', '11187{9}114{6}39', '6', '39'),
('96', '1482', '11187{9}99{6}39', '9', '99'),
('97', '1482', '11187{9}99{6}39', '6', '39'),
('98', '1482', '11187{9}98{6}39', '9', '98'),
('99', '1482', '11187{9}98{6}39', '6', '39'),
('105', '10737', '10066{11}105', '11', '105'),
('315', '18419', '11226{9}110{6}39', '6', '39'),
('293', '14924', '11224{9}98{6}50{10}103', '9', '98'),
('252', '18837', '8316{2}4{1}20', '2', '4'),
('253', '18837', '8316{2}4{1}20', '1', '20'),
('314', '18419', '11226{9}110{6}39', '9', '110'),
('310', '17636', '10066{11}105', '11', '105'),
('301', '14924', '11224{9}100{6}50{10}103', '10', '103'),
('300', '14924', '11224{9}100{6}50{10}103', '6', '50'),
('299', '14924', '11224{9}100{6}50{10}103', '9', '100'),
('298', '14924', '11224{9}99{6}50{10}103', '10', '103'),
('297', '14924', '11224{9}99{6}50{10}103', '6', '50'),
('296', '14924', '11224{9}99{6}50{10}103', '9', '99'),
('295', '14924', '11224{9}98{6}50{10}103', '10', '103'),
('294', '14924', '11224{9}98{6}50{10}103', '6', '50'),
('272', '21472', '9109{3}5', '3', '5'),
('276', '21477', '10066{11}105', '11', '105'),
('227', '21450', '10850{11}104', '11', '104'),
('231', '20212', '10850{11}104', '11', '104'),
('163', '21234', '10782{1}58', '1', '58'),
('951', '2731', '11205{9}131{6}40', '6', '40'),
('333', '21513', '11084{1}24', '1', '24'),
('330', '21511', '9891{3}5', '3', '5'),
('332', '21512', '9109{3}5', '3', '5'),
('336', '15893', '10066{11}105', '11', '105'),
('731', '20704', '11217{9}99{6}40', '6', '40'),
('434', '15049', '10066{11}105', '11', '105'),
('948', '2731', '11205{9}100{6}40', '9', '100'),
('362', '19612', '8076{3}5', '3', '5'),
('398', '20602', '9891{3}5', '3', '5'),
('377', '12531', '10066{11}105', '11', '105'),
('376', '18346', '10066{11}104', '11', '104'),
('378', '20142', '9891{3}5', '3', '5'),
('383', '20439', '9891{3}5', '3', '5'),
('385', '21538', '9891{3}5', '3', '5'),
('524', '21625', '8076{3}5', '3', '5'),
('410', '21547', '10066{11}104', '11', '104'),
('493', '19052', '10498{3}5', '3', '5'),
('547', '21647', '10066{11}105', '11', '105'),
('474', '21581', '7704{3}5', '3', '5'),
('469', '20988', '10851{11}105', '11', '105'),
('460', '20375', '10066{11}104', '11', '104'),
('555', '21660', '11113{1}24', '1', '24'),
('548', '21069', '10851{11}104', '11', '104'),
('528', '21626', '10066{11}104', '11', '104'),
('738', '20704', '5585{4}260{1}197', '4', '260'),
('737', '20704', '12633{4}256{1}198{11}262', '11', '262'),
('736', '20704', '12633{4}256{1}198{11}262', '1', '198'),
('735', '20704', '12633{4}256{1}198{11}262', '4', '256'),
('734', '20704', '11219{9}107{6}40', '6', '40'),
('660', '21242', '10066{11}105', '11', '105'),
('732', '20704', '11217{9}110', '9', '110'),
('733', '20704', '11219{9}107{6}40', '9', '107'),
('730', '20704', '11217{9}99{6}40', '9', '99'),
('807', '21755', '11056{1}27', '1', '27'),
('772', '13030', '10066{11}105', '11', '105'),
('775', '19565', '12637{4}256{1}246{11}262', '4', '256'),
('739', '20704', '5585{4}260{1}197', '1', '197'),
('740', '20704', '5585{4}260{1}246', '4', '260'),
('741', '20704', '5585{4}260{1}246', '1', '246'),
('792', '19279', '10066{11}105', '11', '105'),
('770', '21743', '8992{3}5', '3', '5'),
('776', '19565', '12637{4}256{1}246{11}262', '1', '246'),
('777', '19565', '12637{4}256{1}246{11}262', '11', '262'),
('778', '20359', '5566{1}64', '1', '64'),
('781', '18352', '10850{11}104', '11', '104'),
('900', '21786', '12587{4}47{13}242{1}244', '1', '244'),
('899', '21786', '12587{4}47{13}242{1}244', '13', '242'),
('814', '13177', '9891{3}5', '3', '5'),
('819', '21759', '11097{1}24', '1', '24'),
('859', '18348', '10066{11}105', '11', '105'),
('898', '21786', '12587{4}47{13}242{1}244', '4', '47'),
('856', '20892', '12626{4}47{13}242{1}197', '4', '47'),
('857', '20892', '12626{4}47{13}242{1}197', '13', '242'),
('840', '11480', '9891{3}5', '3', '5'),
('849', '21766', '10850{11}105', '11', '105'),
('858', '20892', '12626{4}47{13}242{1}197', '1', '197'),
('938', '15390', '8076{3}5', '3', '5'),
('950', '2731', '11205{9}131{6}40', '9', '131'),
('927', '21797', '12425{10}103', '10', '103'),
('949', '2731', '11205{9}100{6}40', '6', '40');

COMMIT;
