-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 20, 2025 at 02:23 PM
-- Table: affiliate_payment_status_history
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `affiliate_payment_status_history`
--

DROP TABLE IF EXISTS `affiliate_payment_status_history`;
CREATE TABLE `affiliate_payment_status_history` (
  `affiliate_status_history_id` int(11) NOT NULL AUTO_INCREMENT,
  `affiliate_payment_id` int(11) NOT NULL DEFAULT 0,
  `affiliate_new_value` int(5) NOT NULL DEFAULT 0,
  `affiliate_old_value` int(5) DEFAULT NULL,
  `affiliate_date_added` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `affiliate_notified` int(1) DEFAULT 0,
  <PERSON><PERSON><PERSON><PERSON> KEY (`affiliate_status_history_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

COMMIT;
