-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 20, 2025 at 02:23 PM
-- Table: autodesk_quote_line_items_reference_subscriptions
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autodesk_quote_line_items_reference_subscriptions`
--

DROP TABLE IF EXISTS `autodesk_quote_line_items_reference_subscriptions`;
CREATE TABLE `autodesk_quote_line_items_reference_subscriptions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `line_item_id` int(11) NOT NULL,
  `quantity` int(11) DEFAULT NULL,
  `endDate` date DEFAULT NULL,
  `term` varchar(50) DEFAULT NULL,
  `term_code` varchar(20) DEFAULT NULL,
  `term_description` varchar(255) DEFAULT NULL,
  `offeringCode` varchar(50) DEFAULT NULL,
  `offeringName` varchar(255) DEFAULT NULL,
  `marketingName` varchar(255) DEFAULT NULL,
  `annualDeclaredValue` decimal(10,2) DEFAULT NULL,
  `pricingMethod` varchar(50) DEFAULT NULL,
  `pricingMethod_code` varchar(20) DEFAULT NULL,
  `pricingMethod_description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `line_item_id` (`line_item_id`),
  KEY `idx_line_item_id` (`line_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

COMMIT;
