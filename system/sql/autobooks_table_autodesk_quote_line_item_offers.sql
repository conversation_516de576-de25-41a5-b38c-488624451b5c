-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 20, 2025 at 02:23 PM
-- Table: autodesk_quote_line_item_offers
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autodesk_quote_line_item_offers`
--

DROP TABLE IF EXISTS `autodesk_quote_line_item_offers`;
CREATE TABLE `autodesk_quote_line_item_offers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `line_item_id` int(11) NOT NULL,
  `term_code` varchar(10) DEFAULT NULL,
  `term_description` varchar(100) DEFAULT NULL,
  `access_model_code` varchar(10) DEFAULT NULL,
  `access_model_description` varchar(100) DEFAULT NULL,
  `intended_usage_code` varchar(10) DEFAULT NULL,
  `intended_usage_description` varchar(100) DEFAULT NULL,
  `connectivity_code` varchar(10) DEFAULT NULL,
  `connectivity_description` varchar(100) DEFAULT NULL,
  `connectivity_interval_code` varchar(10) DEFAULT NULL,
  `connectivity_interval_description` varchar(100) DEFAULT NULL,
  `billing_behavior_code` varchar(10) DEFAULT NULL,
  `billing_behavior_description` varchar(100) DEFAULT NULL,
  `billing_type_code` varchar(10) DEFAULT NULL,
  `billing_type_description` varchar(100) DEFAULT NULL,
  `billing_frequency_code` varchar(10) DEFAULT NULL,
  `billing_frequency_description` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `line_item_id` (`line_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

COMMIT;
