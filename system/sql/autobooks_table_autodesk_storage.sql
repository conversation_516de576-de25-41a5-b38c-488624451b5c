-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 20, 2025 at 02:23 PM
-- Table: autodesk_storage
-- Records: 10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autodesk_storage`
--

DROP TABLE IF EXISTS `autodesk_storage`;
CREATE TABLE `autodesk_storage` (
  `autodesk_storage_id` int(11) NOT NULL AUTO_INCREMENT,
  `autodesk_storage_key` varchar(64) NOT NULL,
  `autodesk_storage_data` text NOT NULL,
  PRIMARY KEY (`autodesk_storage_id`),
  UNIQUE KEY `autodesk_storage_key` (`autodesk_storage_key`)
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8;

-- Dumping data for table `autodesk_storage`
--

INSERT INTO `autodesk_storage` (`autodesk_storage_id`, `autodesk_storage_key`, `autodesk_storage_data`) VALUES
('1', 'subscription_renew_email_template', '  <h1 style=\"color: #0078D7;\">RENEW NOW</h1>
    <p><strong>IT’S TIME TO RENEW YOUR AUTODESK SUBSCRIPTION CONTRACT</strong></p>
    <p style=\"color: #666;\">Please disregard this email if you have already renewed.</p>
    
    <p>Dear <strong>{{endCustomerName}}</strong>,</p>
    <p>Your Autodesk subscription expires soon. Please check the contract details below and the renewal procedures.</p>
    
    <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">
        <tr>
            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Product:</strong></td>
            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{product_name}}</td>
        </tr>
        <tr>
            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Terms:</strong></td>
            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{term}}</td>
        </tr>
        <tr>
            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Seats:</strong></td>
            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{seats}}</td>
        </tr>
        <tr>
            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Serial Number:</strong></td>
            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{subscriptionReferenceNumber}}</td>
        </tr>
        <tr>
            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Renewal Number:</strong></td>
            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{$opportunityNumber}}</td>
        </tr>
    </table>
    
    <p>Please reply to this email to receive a quotation for this renewal.</p>
    
    <h2>Why buy from Autodesk Partner?</h2>
    <p>As a partner, we understand your unique business and industry needs. Autodesk Authorized partners develop solutions, implement and provide specialist support services and much more. See below:</p>
    <ul style=\"list-style: disc; margin-left: 20px;\">
        <li>Competitive Pricing</li>
        <li>Experienced Sales Staff</li>
        <li>Dedicated sales contact with phone number</li>
        <li>Access to value-added services and products</li>
        <li>Product support and training</li>
    </ul>
    
    <p>Regards,</p>
    <p><strong>Aurangzaib Mahmood</strong><br>
    TCS CAD & BIM Solutions Limited<br>
    Unit F, Yorkway<br>
    Mandale Industrial Estate<br>
    Thornaby<br>
    Stockton-on-Tees<br>
    TS17 6BX<br>
    Tel: ************</p>
    
    <p style=\"font-size: 0.9em; color: #666;\">You have received this email because your administrator has set up an automated email notification.</p>

                '),
('3', 'subscription_renew_email_send_rules', '-20,-15,-10,0,1,3,5,10,15,30,60,90'),
('4', 'subscription_renew_email_from', '<EMAIL>'),
('5', 'subscription_renew_email_subject', 'Your Autodesk contract is due for Renewal shortly '),
('49', 'subscription_renew_email_send_days', ',true,true,true,true,true'),
('56', 'subscription_renew_email_send_time', '11'),
('63', 'subscription_export_data', '{\"id\":\"7afa32e2c8a0f9813a472c38433dd709\",\"status\":\"processing\",\"password\":\"Y2RlMmY4NDItNjhlMC00OTVlLWI2YWYtMjA0YjE0MWZmM2Y3\"}'),
('87', 'quote_export_data', '\"{\\\"id\\\":\\\"922347a4-712e-405b-bf98-bda0b1e4d62d\\\",\\\"status\\\":\\\"processing\\\",\\\"password\\\":\\\"ODVmMjA2NjAtYzEwOS00N2E4LWJiYzQtMDBmNDMwMjhhM2Nm\\\"}\"'),
('105', 'subscription_table_config', ''),
('106', 'subscription_replacements', '[]');

COMMIT;
