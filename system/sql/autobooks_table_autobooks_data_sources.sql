-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 20, 2025 at 02:23 PM
-- Table: autobooks_data_sources
-- Records: 10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_data_sources`
--

DROP TABLE IF EXISTS `autobooks_data_sources`;
CREATE TABLE `autobooks_data_sources` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the data source',
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional description of what this data source provides',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'other' COMMENT 'Data source category (data_table, email, users, system, autodesk, other)',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT 'Data source status (active, inactive, draft)',
  `table_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Primary database table name (for backward compatibility)',
  `tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of all tables used in this data source',
  `table_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping table names to their aliases (e.g., {"users": "u", "posts": "p"})',
  `joins` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of join configurations with type, tables, columns, and aliases',
  `selected_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of selected columns from all tables',
  `column_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping column keys to their custom aliases (e.g., {"users.first_name": "full_name"})',
  `custom_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of custom SQL columns with expressions and aliases',
  `filters` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of filter configurations for WHERE clauses',
  `sorting` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of sorting rules for ORDER BY clauses',
  `limits` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object with limit and offset settings for pagination',
  `column_mapping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON column mapping configuration for advanced use cases',
  `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'When the data source was created',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'When the data source was last updated',
  `grouping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of grouping rules (GROUP BY)',
  `custom_tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of custom SQL tables and subqueries with aliases',
  `data_source_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'standard' COMMENT 'Type of data source: standard or multi_table_merger',
  `table_patterns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of wildcard patterns for multi-table merger',
  `explicit_tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of explicitly selected tables for multi-table merger',
  `resolved_tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of resolved table names from patterns and explicit selection',
  `mapping_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Column mapping method for multi-table merger: like_for_like, manual, unified_field_mapper',
  `column_mappings` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of manual column mappings for multi-table merger',
  `unified_mappings` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object of unified field mapper mappings for multi-table merger',
  `reference_table` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Reference table name for column mapping assistance',
  `search_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of searchable column names for full-text search',
  PRIMARY KEY (`id`),
  KEY `idx_status_category` (`status`,`category`) COMMENT 'Index for filtering by status and category',
  KEY `idx_table_name` (`table_name`) COMMENT 'Index for backward compatibility queries',
  KEY `idx_created_by` (`created_by`) COMMENT 'Index for user-specific queries',
  KEY `idx_created_at` (`created_at`) COMMENT 'Index for chronological queries',
  KEY `idx_name` (`name`),
  KEY `idx_category_status` (`category`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns with full multi-table support, aliases, custom columns, sorting, and limits';

-- Dumping data for table `autobooks_data_sources`
--

INSERT INTO `autobooks_data_sources` (`id`, `name`, `description`, `category`, `status`, `table_name`, `tables`, `table_aliases`, `joins`, `selected_columns`, `column_aliases`, `custom_columns`, `filters`, `sorting`, `limits`, `column_mapping`, `created_by`, `created_at`, `updated_at`, `grouping`, `custom_tables`, `data_source_type`, `table_patterns`, `explicit_tables`, `resolved_tables`, `mapping_method`, `column_mappings`, `unified_mappings`, `reference_table`, `search_columns`) VALUES
('1', 'Autodesk_autorenew', '', 'other', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"}]', '{\"autodesk_subscriptions\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\",\"paymentMethod\",\"offeringId\",\"offeringCode\",\"offeringName\",\"autoRenew\"],\"autodesk_accounts\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\",\"email\",\"city\",\"postal_code\"]}', '[]', '[]', '[{\"column\":\"autoRenew\",\"operator\":\"=\",\"value\":\"ON\"},{\"column\":\"status\",\"operator\":\"=\",\"value\":\"Active\"}]', '[]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-07-25 15:52:00', '2025-07-28 10:56:21', '[{\"column\":\"autodesk_accounts.account_csn\"}]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('30', 'Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"],\"lastquote\":[\"quote_id\",\"quote_number\",\"quoted_date\"]}', '[]', '[{\"sql\":\"CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END\",\"alias\":\"subs_enddatediff\"}]', '[]', '[{\"column\":\"subs_enddatediff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', NULL, '1', '2025-07-31 19:32:34', '2025-08-13 11:15:51', '[]', '[{\"alias\":\"lastquote\",\"sql\":\"SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id\\n\\nFROM autodesk_quote_line_items qi\\nJOIN autodesk_quotes q ON q.id = qi.quote_id\\n\\nWHERE qi.subscription_id IS NOT NULL\\n  AND q.quote_status NOT IN (\'Expired\', \'Cancelled\')\\n\\nORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC\\n\\nLIMIT 1\",\"join_type\":\"LEFT JOIN\",\"join_condition\":\"subs.subscriptionId = lastquote.subscription_id\",\"columns\":\"quote_id, quote_status, quote_number, quoted_date\",\"description\":\"\"}]', 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '[\"autodesk_subscriptions.subscriptionId\",\"autodesk_subscriptions.subscriptionReferenceNumber\",\"autodesk_subscriptions.status\",\"autodesk_subscriptions.accessModel\",\"autodesk_subscriptions.billingBehavior\",\"autodesk_subscriptions.billingFrequency\",\"autodesk_subscriptions.connectivity\",\"autodesk_subscriptions.connectivityInterval\",\"autodesk_subscriptions.intendedUsage\",\"autodesk_subscriptions.servicePlan\",\"autodesk_subscriptions.term\",\"autodesk_subscriptions.paymentMethod\",\"autodesk_subscriptions.offeringCode\",\"autodesk_subscriptions.offeringName\",\"autodesk_subscriptions.recordType\",\"autodesk_subscriptions.autoRenew\",\"autodesk_subscriptions.opportunityNumber\",\"autodesk_subscriptions.soldTo_id\",\"autodesk_subscriptions.solutionProvider_id\",\"autodesk_subscriptions.nurtureReseller_id\",\"autodesk_subscriptions.endCustomer_id\"]'),
('31', 'Autodesk Accounts', 'Customer account information', 'autodesk', 'active', 'autodesk_accounts', '[\"autodesk_accounts\"]', '[]', '[]', '[]', '[]', '[]', '[]', '[{\"column\":\"autodesk_accounts_id\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', NULL, '1', '2025-07-31 19:32:34', '2025-08-20 06:54:03', '[]', '[]', 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('32', 'Expiring Subscriptions', 'Active subscriptions expiring within 90 days', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', NULL, '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_id\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.id\",\"left_alias\":\"\",\"right_alias\":\"soldto\"}]', '{\"autodesk_subscriptions\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\",\"offeringCode\",\"offeringName\",\"autoRenew\",\"soldTo_id\"],\"autodesk_accounts\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\",\"email\",\"city\",\"postal_code\"]}', '{\"subscriptionId\":\"subscription_id\",\"offeringName\":\"product_name\",\"endDate\":\"expiry_date\",\"quantity\":\"quantity\",\"soldTo_id\":\"customer_id\"}', NULL, '[{\"column\":\"status\",\"operator\":\"=\",\"value\":\"Active\"},{\"column\":\"endDate\",\"operator\":\">\",\"value\":\"2024-01-01\"},{\"column\":\"endDate\",\"operator\":\"<\",\"value\":\"2024-12-31\"}]', NULL, NULL, NULL, '1', '2025-07-31 19:32:34', '2025-07-31 20:32:34', NULL, NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('33', 'Autodesk Email History', 'Email communication history', 'autodesk', 'active', 'autodesk_email_history', '[\"autodesk_email_history\",\"autodesk_subscriptions\"]', '[]', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_email_history\",\"left_column\":\"autodesk_email_history.subscription_id\",\"right_table\":\"autodesk_subscriptions\",\"right_column\":\"autodesk_subscriptions.id\",\"left_alias\":\"\",\"right_alias\":\"sub\"}]', '{\"autodesk_email_history\":[\"id\",\"subscription_id\"]}', '[]', '[]', '[]', '[]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', NULL, '1', '2025-07-31 19:32:34', '2025-08-01 15:42:32', '[]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('34', 'Copy of Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"soldto\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.solutionProvider_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"solpro\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.nurtureReseller_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"resell\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"]}', '[]', '[{\"sql\":\"DATEDIFF(subs.enddate, NOW())\",\"alias\":\"enddate_diff\"}]', '[]', '[{\"column\":\"enddate_diff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-02 09:30:09', '2025-08-02 09:30:09', '[]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('35', 'Full Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"soldto\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.solutionProvider_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"solpro\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.nurtureReseller_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"resell\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"]}', '[]', '[{\"sql\":\"DATEDIFF(subs.enddate, NOW())\",\"alias\":\"enddate_diff\"}]', '[]', '[{\"column\":\"enddate_diff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-02 09:31:53', '2025-08-02 09:35:28', '[]', NULL, 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('51', 'tstAutodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.soldTo_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"soldto\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.solutionProvider_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"solpro\"},{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.nurtureReseller_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"resell\"}]', '{\"subs\":[\"id\",\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\"],\"endcust\":[\"id\",\"account_csn\",\"name\",\"first_name\",\"last_name\"]}', '[]', '[{\"sql\":\"DATEDIFF(subs.enddate, NOW())\",\"alias\":\"enddate_diff\"}]', '[]', '[{\"column\":\"enddate_diff\",\"direction\":\"ASC\"}]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-15 13:47:58', '2025-08-15 13:49:31', '[]', '[]', 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('63', 'Autodesk_products', '', 'autodesk', 'active', 'products_autodesk_catalog', '[\"products_autodesk_catalog\"]', '[]', '[]', '[]', '[]', '[]', '[]', '[]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', '2', '2025-08-18 08:30:38', '2025-08-18 08:30:54', '[]', '[]', 'standard', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('77', 'CSV Import: Sketchup', 'Auto-generated data source for CSV import (79 rows, 59 columns)', 'csv_import', 'active', 'autobooks_import_sketchup_data', '[\"autobooks_import_sketchup_data\"]', '{\"autobooks_import_sketchup_data\":\"sketchup\"}', NULL, '{\"autobooks_import_sketchup_data\":[\"sold_to_name\",\"sold_to_number\",\"vendor_name\",\"reseller_number\",\"reseller_vendor_id\",\"end_customer_vendor_id\",\"end_customer_name\",\"end_customer_address_1\",\"end_customer_address_2\",\"end_customer_address_3\",\"end_customer_city\",\"end_customer_state\",\"end_customer_zip_code\",\"end_customer_country\",\"end_customer_account_type\",\"end_customer_contact_name\",\"end_customer_contact_email\",\"end_customer_contact_phone\",\"end_customer_industry_segment\",\"agreement_program_name\",\"agreement_number\",\"agreement_start_date\",\"agreement_end_date\",\"agreement_terms\",\"agreement_type\",\"agreement_status\",\"agreement_support_level\",\"agreement_days_due\",\"agreement_autorenew\",\"product_name\",\"product_family\",\"product_market_segment\",\"product_release\",\"product_type\",\"product_deployment\",\"product_sku\",\"product_sku_description\",\"product_part\",\"product_list_price\",\"product_list_price_currency\",\"subscription_id\",\"subscription_serial_number\",\"subscription_status\",\"subscription_quantity\",\"subscription_start_date\",\"subscription_end_date\",\"subscription_contact_name\",\"subscription_contact_email\",\"subscription_level\",\"subscription_days_due\",\"quotation_id\",\"quotation_type\",\"quotation_vendor_id\",\"quotation_deal_registration_number\",\"quotation_status\",\"quotation_resellerpo_previous\",\"quotation_due_date\",\"flaer_phase\",\"updated\"]}', '{\"autobooks_import_sketchup_data.id\":\"sketchup_id\",\"sketchup.id\":\"sketchup_id\",\"autobooks_import_sketchup_data.sold_to_name\":\"company_name\",\"sketchup.sold_to_name\":\"company_name\",\"autobooks_import_sketchup_data.sold_to_number\":\"sketchup_sold_to_number\",\"sketchup.sold_to_number\":\"sketchup_sold_to_number\",\"autobooks_import_sketchup_data.vendor_name\":\"contact_name\",\"sketchup.vendor_name\":\"contact_name\",\"autobooks_import_sketchup_data.reseller_number\":\"reseller_name\",\"sketchup.reseller_number\":\"reseller_name\",\"autobooks_import_sketchup_data.reseller_vendor_id\":\"sketchup_reseller_vendor_id\",\"sketchup.reseller_vendor_id\":\"sketchup_reseller_vendor_id\",\"autobooks_import_sketchup_data.end_customer_vendor_id\":\"sketchup_end_customer_vendor_id\",\"sketchup.end_customer_vendor_id\":\"sketchup_end_customer_vendor_id\",\"autobooks_import_sketchup_data.end_customer_name\":\"sketchup_end_customer_name\",\"sketchup.end_customer_name\":\"sketchup_end_customer_name\",\"autobooks_import_sketchup_data.end_customer_address_1\":\"address\",\"sketchup.end_customer_address_1\":\"address\",\"autobooks_import_sketchup_data.end_customer_address_2\":\"sketchup_end_customer_address_2\",\"sketchup.end_customer_address_2\":\"sketchup_end_customer_address_2\",\"autobooks_import_sketchup_data.end_customer_address_3\":\"sketchup_end_customer_address_3\",\"sketchup.end_customer_address_3\":\"sketchup_end_customer_address_3\",\"autobooks_import_sketchup_data.end_customer_city\":\"city\",\"sketchup.end_customer_city\":\"city\",\"autobooks_import_sketchup_data.end_customer_state\":\"state\",\"sketchup.end_customer_state\":\"state\",\"autobooks_import_sketchup_data.end_customer_zip_code\":\"postal_code\",\"sketchup.end_customer_zip_code\":\"postal_code\",\"autobooks_import_sketchup_data.end_customer_country\":\"country\",\"sketchup.end_customer_country\":\"country\",\"autobooks_import_sketchup_data.end_customer_account_type\":\"sketchup_end_customer_account_type\",\"sketchup.end_customer_account_type\":\"sketchup_end_customer_account_type\",\"autobooks_import_sketchup_data.end_customer_contact_name\":\"sketchup_end_customer_contact_name\",\"sketchup.end_customer_contact_name\":\"sketchup_end_customer_contact_name\",\"autobooks_import_sketchup_data.end_customer_contact_email\":\"email\",\"sketchup.end_customer_contact_email\":\"email\",\"autobooks_import_sketchup_data.end_customer_contact_phone\":\"sketchup_end_customer_contact_phone\",\"sketchup.end_customer_contact_phone\":\"sketchup_end_customer_contact_phone\",\"autobooks_import_sketchup_data.end_customer_industry_segment\":\"sketchup_end_customer_industry_segment\",\"sketchup.end_customer_industry_segment\":\"sketchup_end_customer_industry_segment\",\"autobooks_import_sketchup_data.agreement_program_name\":\"product_name\",\"sketchup.agreement_program_name\":\"product_name\",\"autobooks_import_sketchup_data.agreement_number\":\"subscription_reference\",\"sketchup.agreement_number\":\"subscription_reference\",\"autobooks_import_sketchup_data.agreement_start_date\":\"start_date\",\"sketchup.agreement_start_date\":\"start_date\",\"autobooks_import_sketchup_data.agreement_end_date\":\"end_date\",\"sketchup.agreement_end_date\":\"end_date\",\"autobooks_import_sketchup_data.agreement_terms\":\"sketchup_agreement_terms\",\"sketchup.agreement_terms\":\"sketchup_agreement_terms\",\"autobooks_import_sketchup_data.agreement_type\":\"sketchup_agreement_type\",\"sketchup.agreement_type\":\"sketchup_agreement_type\",\"autobooks_import_sketchup_data.agreement_status\":\"status\",\"sketchup.agreement_status\":\"status\",\"autobooks_import_sketchup_data.agreement_support_level\":\"sketchup_agreement_support_level\",\"sketchup.agreement_support_level\":\"sketchup_agreement_support_level\",\"autobooks_import_sketchup_data.agreement_days_due\":\"sketchup_agreement_days_due\",\"sketchup.agreement_days_due\":\"sketchup_agreement_days_due\",\"autobooks_import_sketchup_data.agreement_autorenew\":\"sketchup_agreement_autorenew\",\"sketchup.agreement_autorenew\":\"sketchup_agreement_autorenew\",\"autobooks_import_sketchup_data.product_name\":\"sketchup_product_name\",\"sketchup.product_name\":\"sketchup_product_name\",\"autobooks_import_sketchup_data.product_family\":\"product_name\",\"sketchup.product_family\":\"product_name\",\"autobooks_import_sketchup_data.product_market_segment\":\"sketchup_product_market_segment\",\"sketchup.product_market_segment\":\"sketchup_product_market_segment\",\"autobooks_import_sketchup_data.product_release\":\"sketchup_product_release\",\"sketchup.product_release\":\"sketchup_product_release\",\"autobooks_import_sketchup_data.product_type\":\"sketchup_product_type\",\"sketchup.product_type\":\"sketchup_product_type\",\"autobooks_import_sketchup_data.product_deployment\":\"sketchup_product_deployment\",\"sketchup.product_deployment\":\"sketchup_product_deployment\",\"autobooks_import_sketchup_data.product_sku\":\"sketchup_product_sku\",\"sketchup.product_sku\":\"sketchup_product_sku\",\"autobooks_import_sketchup_data.product_sku_description\":\"sketchup_product_sku_description\",\"sketchup.product_sku_description\":\"sketchup_product_sku_description\",\"autobooks_import_sketchup_data.product_part\":\"sketchup_product_part\",\"sketchup.product_part\":\"sketchup_product_part\",\"autobooks_import_sketchup_data.product_list_price\":\"price\",\"sketchup.product_list_price\":\"price\",\"autobooks_import_sketchup_data.product_list_price_currency\":\"currency\",\"sketchup.product_list_price_currency\":\"currency\",\"autobooks_import_sketchup_data.subscription_id\":\"sketchup_subscription_id\",\"sketchup.subscription_id\":\"sketchup_subscription_id\",\"autobooks_import_sketchup_data.subscription_serial_number\":\"serial_number\",\"sketchup.subscription_serial_number\":\"serial_number\",\"autobooks_import_sketchup_data.subscription_status\":\"sketchup_subscription_status\",\"sketchup.subscription_status\":\"sketchup_subscription_status\",\"autobooks_import_sketchup_data.subscription_quantity\":\"quantity\",\"sketchup.subscription_quantity\":\"quantity\",\"autobooks_import_sketchup_data.subscription_start_date\":\"sketchup_subscription_start_date\",\"sketchup.subscription_start_date\":\"sketchup_subscription_start_date\",\"autobooks_import_sketchup_data.subscription_end_date\":\"sketchup_subscription_end_date\",\"sketchup.subscription_end_date\":\"sketchup_subscription_end_date\",\"autobooks_import_sketchup_data.subscription_contact_name\":\"sketchup_subscription_contact_name\",\"sketchup.subscription_contact_name\":\"sketchup_subscription_contact_name\",\"autobooks_import_sketchup_data.subscription_contact_email\":\"sketchup_subscription_contact_email\",\"sketchup.subscription_contact_email\":\"sketchup_subscription_contact_email\",\"autobooks_import_sketchup_data.subscription_level\":\"sketchup_subscription_level\",\"sketchup.subscription_level\":\"sketchup_subscription_level\",\"autobooks_import_sketchup_data.subscription_days_due\":\"sketchup_subscription_days_due\",\"sketchup.subscription_days_due\":\"sketchup_subscription_days_due\",\"autobooks_import_sketchup_data.quotation_id\":\"sketchup_quotation_id\",\"sketchup.quotation_id\":\"sketchup_quotation_id\",\"autobooks_import_sketchup_data.quotation_type\":\"sketchup_quotation_type\",\"sketchup.quotation_type\":\"sketchup_quotation_type\",\"autobooks_import_sketchup_data.quotation_vendor_id\":\"sketchup_quotation_vendor_id\",\"sketchup.quotation_vendor_id\":\"sketchup_quotation_vendor_id\",\"autobooks_import_sketchup_data.quotation_deal_registration_number\":\"sketchup_quotation_deal_registration_number\",\"sketchup.quotation_deal_registration_number\":\"sketchup_quotation_deal_registration_number\",\"autobooks_import_sketchup_data.quotation_status\":\"sketchup_quotation_status\",\"sketchup.quotation_status\":\"sketchup_quotation_status\",\"autobooks_import_sketchup_data.quotation_resellerpo_previous\":\"sketchup_quotation_resellerpo_previous\",\"sketchup.quotation_resellerpo_previous\":\"sketchup_quotation_resellerpo_previous\",\"autobooks_import_sketchup_data.quotation_due_date\":\"sketchup_quotation_due_date\",\"sketchup.quotation_due_date\":\"sketchup_quotation_due_date\",\"autobooks_import_sketchup_data.flaer_phase\":\"sketchup_flaer_phase\",\"sketchup.flaer_phase\":\"sketchup_flaer_phase\",\"autobooks_import_sketchup_data.updated\":\"sketchup_updated\",\"sketchup.updated\":\"sketchup_updated\",\"autobooks_import_sketchup_data.created_at\":\"sketchup_created_at\",\"sketchup.created_at\":\"sketchup_created_at\",\"autobooks_import_sketchup_data.updated_at\":\"sketchup_updated_at\",\"sketchup.updated_at\":\"sketchup_updated_at\"}', NULL, NULL, NULL, NULL, NULL, '2', '2025-08-20 12:35:01', '2025-08-20 12:35:01', NULL, NULL, 'standard', NULL, NULL, '[\"autobooks_import_sketchup_data\"]', 'unified_field_mapper', NULL, '{\"min_confidence\":75,\"applied\":{\"sold_to_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":100,\"final_score\":67.4,\"normalized_fields\":[\"company_name\\r\",\"endcust_name\\r\",\"end_customer_name\"]},\"vendor_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":90,\"final_score\":63.4,\"normalized_fields\":[\"company_name\\r\",\"endcust_name\\r\",\"end_customer_name\"]},\"reseller_number\":{\"category\":\"reseller_name\",\"field_name\":\"reseller_name\",\"confidence\":80,\"final_score\":54.5,\"normalized_fields\":[\"reseller_name\\r\",\"partner_name\\r\",\"account_primary_reseller_name\"]},\"reseller_vendor_id\":{\"category\":\"reseller_name\",\"field_name\":\"reseller_name\",\"confidence\":80,\"final_score\":54.5,\"normalized_fields\":[\"reseller_name\\r\",\"partner_name\\r\",\"account_primary_reseller_name\"]},\"end_customer_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":100,\"final_score\":67.4,\"normalized_fields\":[\"company_name\\r\",\"endcust_name\\r\",\"end_customer_name\"]},\"end_customer_address_1\":{\"category\":\"address\",\"field_name\":\"address\",\"confidence\":100,\"final_score\":70,\"normalized_fields\":[\"address\\r\",\"end_customer_address_1\"]},\"end_customer_city\":{\"category\":\"city\",\"field_name\":\"city\",\"confidence\":100,\"final_score\":61,\"normalized_fields\":[\"city\\r\",\"end_customer_city\"]},\"end_customer_state\":{\"category\":\"state\",\"field_name\":\"state\",\"confidence\":100,\"final_score\":61,\"normalized_fields\":[\"state\\r\",\"end_customer_state\"]},\"end_customer_zip_code\":{\"category\":\"postal_code\",\"field_name\":\"postal_code\",\"confidence\":100,\"final_score\":61,\"normalized_fields\":[\"postal_code\\r\",\"end_customer_zip_code\"]},\"end_customer_country\":{\"category\":\"country\",\"field_name\":\"country\",\"confidence\":100,\"final_score\":61,\"normalized_fields\":[\"country\\r\",\"end_customer_country\"]},\"end_customer_contact_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":90,\"final_score\":63.4,\"normalized_fields\":[\"company_name\\r\",\"endcust_name\\r\",\"end_customer_name\"]},\"end_customer_contact_email\":{\"category\":\"email\",\"field_name\":\"email\",\"confidence\":100,\"final_score\":82,\"normalized_fields\":[\"email_address\",\"endcust_primary_admin_email\",\"end_customer_contact_email\"]},\"agreement_program_name\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":100,\"final_score\":70,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"agreement_number\":{\"category\":\"subscription_reference\",\"field_name\":\"subscription_reference\",\"confidence\":100,\"final_score\":85,\"normalized_fields\":[\"subscription_reference\\r\",\"subs_subscriptionReferenceNumber\\r\",\"subscription_id\"]},\"agreement_start_date\":{\"category\":\"start_date\",\"field_name\":\"start_date\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"start_date\\r\",\"subs_startDate\\r\",\"agreement_start_date\"]},\"agreement_end_date\":{\"category\":\"end_date\",\"field_name\":\"end_date\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"end_date\\r\",\"subs_endDate\\r\",\"agreement_end_date\"]},\"agreement_status\":{\"category\":\"status\",\"field_name\":\"status\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"status\\r\",\"subs_status\\r\",\"subscription_status\"]},\"product_name\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":100,\"final_score\":70,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_family\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":100,\"final_score\":79,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_market_segment\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_release\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_type\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_deployment\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_sku\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_sku_description\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_part\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_list_price\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"product_list_price_currency\":{\"category\":\"product_name\",\"field_name\":\"product_name\",\"confidence\":80,\"final_score\":62,\"normalized_fields\":[\"product_name\\r\",\"subs_offeringName\\r\",\"agreement_program_name\"]},\"subscription_id\":{\"category\":\"subscription_reference\",\"field_name\":\"subscription_reference\",\"confidence\":100,\"final_score\":85,\"normalized_fields\":[\"subscription_reference\\r\",\"subs_subscriptionReferenceNumber\\r\",\"subscription_id\"]},\"subscription_serial_number\":{\"category\":\"subscription_reference\",\"field_name\":\"subscription_reference\",\"confidence\":85,\"final_score\":79,\"normalized_fields\":[\"subscription_reference\\r\",\"subs_subscriptionReferenceNumber\\r\",\"subscription_id\"]},\"subscription_status\":{\"category\":\"status\",\"field_name\":\"status\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"status\\r\",\"subs_status\\r\",\"subscription_status\"]},\"subscription_quantity\":{\"category\":\"quantity\",\"field_name\":\"quantity\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"quantity\\r\",\"subs_quantity\\r\",\"subscription_quantity\"]},\"subscription_start_date\":{\"category\":\"start_date\",\"field_name\":\"start_date\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"start_date\\r\",\"subs_startDate\\r\",\"agreement_start_date\"]},\"subscription_end_date\":{\"category\":\"end_date\",\"field_name\":\"end_date\",\"confidence\":100,\"final_score\":58,\"normalized_fields\":[\"end_date\\r\",\"subs_endDate\\r\",\"agreement_end_date\"]},\"subscription_contact_name\":{\"category\":\"company_name\",\"field_name\":\"company_name\",\"confidence\":90,\"final_score\":63.4,\"normalized_fields\":[\"company_name\\r\",\"endcust_name\\r\",\"end_customer_name\"]},\"subscription_contact_email\":{\"category\":\"email\",\"field_name\":\"email\",\"confidence\":100,\"final_score\":82,\"normalized_fields\":[\"email_address\",\"endcust_primary_admin_email\",\"end_customer_contact_email\"]},\"quotation_status\":{\"category\":\"status\",\"field_name\":\"status\",\"confidence\":85,\"final_score\":52,\"normalized_fields\":[\"status\\r\",\"subs_status\\r\",\"subscription_status\"]},\"quotation_due_date\":{\"category\":\"end_date\",\"field_name\":\"end_date\",\"confidence\":85,\"final_score\":52,\"normalized_fields\":[\"end_date\\r\",\"subs_endDate\\r\",\"agreement_end_date\"]}},\"overrides\":[]}', NULL, NULL);

COMMIT;
